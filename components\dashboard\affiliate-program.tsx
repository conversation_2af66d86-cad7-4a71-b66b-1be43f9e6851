"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Lock,
  Users,
  DollarSign,
  TrendingUp,
  CheckCircle,
  Award,
  Target,
  Star,
  Crown,
  Zap
} from "lucide-react"

export default function AffiliateProgram() {
  // Simulate eligibility (user needs 5 referrals to apply)
  const [currentReferrals] = useState(3) // Change this to test different states
  const [eligible] = useState(currentReferrals >= 5)
  const [applied, setApplied] = useState(false)

  const affiliatePlans = [
    {
      id: "starter",
      name: "Starter Affiliate",
      referralsRequired: "5-15",
      commission: "15%",
      features: [
        "Basic marketing materials",
        "Monthly payouts",
        "Email support",
        "Basic analytics dashboard"
      ],
      popular: false,
      icon: Target
    },
    {
      id: "professional",
      name: "Professional Affiliate",
      referralsRequired: "16-50",
      commission: "20%",
      features: [
        "Premium marketing materials",
        "Bi-weekly payouts",
        "Priority support",
        "Advanced analytics",
        "Custom landing pages"
      ],
      popular: true,
      icon: Award
    },
    {
      id: "elite",
      name: "Elite Affiliate",
      referralsRequired: "51+",
      commission: "25%",
      features: [
        "Exclusive marketing materials",
        "Weekly payouts",
        "Dedicated account manager",
        "Real-time analytics",
        "Custom branding",
        "Special bonuses"
      ],
      popular: false,
      icon: Crown
    }
  ]

  const benefits = [
    {
      title: "High Commission Rates",
      description: "Earn up to 25% commission on every successful referral",
      icon: DollarSign
    },
    {
      title: "Marketing Support",
      description: "Access to professional banners, landing pages, and promotional materials",
      icon: Target
    },
    {
      title: "Real-time Tracking",
      description: "Monitor your referrals and earnings with our advanced dashboard",
      icon: TrendingUp
    },
    {
      title: "Fast Payouts",
      description: "Get paid weekly, bi-weekly, or monthly based on your tier",
      icon: Zap
    }
  ]

  const handleApply = () => {
    setApplied(true)
  }

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 text-white p-6 rounded-lg">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">Affiliate Program</h1>
            <p className="text-blue-100">Earn high commissions by promoting our trading challenges</p>
          </div>
          <div className="flex items-center gap-6 text-sm">
            <div className="flex items-center gap-2">
              <Users className="w-4 h-4" />
              <span>{currentReferrals} Referrals</span>
            </div>
            <div className="flex items-center gap-2">
              <DollarSign className="w-4 h-4" />
              <span>$0.00 Earned</span>
            </div>
          </div>
        </div>
      </div>

      {/* Requirements Alert */}
      {!eligible && (
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-start gap-4">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
              <Lock className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Unlock Affiliate Program</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                To apply for our affiliate program, you need at least 5 successful referrals.
              </p>
              <div className="space-y-2">
                <div className="flex items-center gap-3">
                  <div className="w-6 h-6 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                    <span className="text-xs font-bold text-gray-600 dark:text-gray-400">1</span>
                  </div>
                  <span className="text-gray-700 dark:text-gray-300">Make at least 5 successful referrals</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-6 h-6 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                    <span className="text-xs font-bold text-gray-600 dark:text-gray-400">2</span>
                  </div>
                  <span className="text-gray-700 dark:text-gray-300">Apply for affiliate program</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-6 h-6 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                    <span className="text-xs font-bold text-gray-600 dark:text-gray-400">3</span>
                  </div>
                  <span className="text-gray-700 dark:text-gray-300">Get approved and start earning higher commissions</span>
                </div>
              </div>
              <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  <strong>Progress:</strong> {currentReferrals}/5 referrals completed ({Math.round((currentReferrals/5)*100)}%)
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Benefits Section */}
      <div className="grid md:grid-cols-2 gap-6">
        <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
          <CardHeader>
            <CardTitle className="text-gray-900 dark:text-white flex items-center gap-2">
              <Star className="w-5 h-5 text-blue-600" />
              Affiliate Benefits
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-gray-600 dark:text-gray-400 text-sm">
              Join our affiliate program and enjoy these exclusive benefits.
            </p>
            <div className="space-y-3">
              {benefits.map((benefit, index) => {
                const IconComponent = benefit.icon
                return (
                  <div key={index} className="flex items-start gap-3 p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                    <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                      <IconComponent className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-white mb-1">{benefit.title}</h4>
                      <p className="text-gray-600 dark:text-gray-400 text-sm">{benefit.description}</p>
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* Application Status */}
        <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
          <CardHeader>
            <CardTitle className="text-gray-900 dark:text-white flex items-center gap-2">
              <TrendingUp className="w-5 h-5 text-blue-600" />
              Application Status
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {eligible ? (
              applied ? (
                <div className="space-y-3">
                  <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <CheckCircle className="w-8 h-8 text-green-600 mx-auto mb-2" />
                    <h3 className="font-semibold text-green-700 dark:text-green-400">Application Submitted!</h3>
                    <p className="text-sm text-green-600 dark:text-green-400 mt-1">
                      We'll review your application within 2-3 business days.
                    </p>
                  </div>
                </div>
              ) : (
                <div className="space-y-3">
                  <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <CheckCircle className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                    <h3 className="font-semibold text-blue-700 dark:text-blue-400">You're Eligible!</h3>
                    <p className="text-sm text-blue-600 dark:text-blue-400 mt-1">
                      You have {currentReferrals} referrals and can now apply for our affiliate program.
                    </p>
                  </div>
                  <Button
                    onClick={handleApply}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    Apply Now
                  </Button>
                </div>
              )
            ) : (
              <div className="space-y-3">
                <div className="text-center p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                  <Lock className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <h3 className="font-semibold text-gray-700 dark:text-gray-400">Not Eligible Yet</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    You need {5 - currentReferrals} more referrals to apply.
                  </p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Affiliate Plans */}
      <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
        <CardHeader>
          <CardTitle className="text-gray-900 dark:text-white flex items-center gap-2">
            <Award className="w-5 h-5 text-blue-600" />
            Affiliate Plans
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {affiliatePlans.map((plan) => {
              const IconComponent = plan.icon
              return (
                <div
                  key={plan.id}
                  className={`relative bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-6 transition-all duration-300 hover:shadow-md ${
                    plan.popular ? "ring-2 ring-blue-500" : ""
                  }`}
                >
                  {plan.popular && (
                    <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                      <Badge className="bg-blue-600 text-white">
                        Most Popular
                      </Badge>
                    </div>
                  )}

                  <div className="text-center mb-4">
                    <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mx-auto mb-3">
                      <IconComponent className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                      {plan.name}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400 text-sm mb-2">
                      {plan.referralsRequired} referrals required
                    </p>
                    <div className="text-3xl font-bold text-blue-600 mb-4">
                      {plan.commission}
                    </div>
                  </div>

                  <ul className="space-y-2 mb-6">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                        <CheckCircle className="w-4 h-4 text-blue-500 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>

                  <Button
                    className={`w-full ${
                      plan.popular
                        ? "bg-blue-600 hover:bg-blue-700 text-white"
                        : "bg-gray-100 hover:bg-gray-200 text-gray-700 dark:bg-gray-600 dark:hover:bg-gray-500 dark:text-gray-200"
                    }`}
                    disabled={!eligible}
                  >
                    {eligible ? "Choose Plan" : "Requires 5 Referrals"}
                  </Button>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
