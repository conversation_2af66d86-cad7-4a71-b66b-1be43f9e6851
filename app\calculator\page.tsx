"use client"

import { <PERSON><PERSON><PERSON>, ArrowRight } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useLanguage } from "@/contexts/language-context"
import { useEffect } from "react"

export default function CalculatorPage() {
  const { t } = useLanguage()

  useEffect(() => {
    // Load the remote widgets script
    const script1 = document.createElement('script')
    script1.type = 'text/javascript'
    script1.src = 'https://fxverify.com/Content/remote/remote-widgets.js'
    document.head.appendChild(script1)

    // Load the calculator script after the remote widgets script loads
    script1.onload = () => {
      const script2 = document.createElement('script')
      script2.type = 'text/javascript'
      
      // Check if dark mode is active
      const isDarkMode = document.documentElement.classList.contains('dark')
      
      // Define styles based on theme
      const topPaneStyle = isDarkMode 
        ? "YmFja2dyb3VuZDogIzFmMjkyZTsgY29sb3I6IHdoaXRlOyBib3JkZXI6IHNvbGlkIDFweCAjMzc0MTUxOyBib3JkZXItYm90dG9tOiBub25lOyA="
        : "YmFja2dyb3VuZDogd2hpdGU7IGNvbG9yOiBibGFjazsgYm9yZGVyOiBzb2xpZCAxcHggYmxhY2s7IGJvcmRlci1ib3R0b206IG5vbmU7IA=="
      
      const bottomPaneStyle = isDarkMode
        ? "YmFja2dyb3VuZDogIzFmMjkyZTsgYm9yZGVyOiBzb2xpZCAxcHggIzM3NDE1MTsgY29sb3I6IHdoaXRlOw=="
        : "YmFja2dyb3VuZDogd2hpdGU7IGJvcmRlcjogc29saWQgMXB4IGJsYWNrOyBjb2xvcjogYmxhY2s7"
      
      const buttonStyle = isDarkMode
        ? "YmFja2dyb3VuZDogIzM3NDE1MTsgY29sb3I6IHdoaXRlOyBib3JkZXItcmFkaXVzOiAyMHB4OyA="
        : "YmFja2dyb3VuZDogYmxhY2s7IGNvbG9yOiB3aGl0ZTsgYm9yZGVyLXJhZGl1czogMjBweDs="
      
      const titleStyle = isDarkMode
        ? "dGV4dC1hbGlnbjogbGVmdDsgZm9udC1zaXplOiA0MHB4OyBmb250LXdlaWdodDogNTAwOyBjb2xvcjogd2hpdGU7"
        : "dGV4dC1hbGlnbjogbGVmdDsgZm9udC1zaXplOiA0MHB4OyBmb250LXdlaWdodDogNTAwOw=="
      
      const textboxStyle = isDarkMode
        ? "YmFja2dyb3VuZC1jb2xvcjogIzM3NDE1MTsgY29sb3I6IHdoaXRlOyBib3JkZXI6IHNvbGlkIDFweCAjNjc3MzdlOw=="
        : "YmFja2dyb3VuZC1jb2xvcjogd2hpdGU7IGNvbG9yOiBibGFjazsgYm9yZGVyOiBzb2xpZCAxcHggI2FhYWFhYQ=="
      
      script2.textContent = `
        RemoteCalc({
          "Url": "https://fxverify.com", 
          "TopPaneStyle": "${topPaneStyle}",
          "BottomPaneStyle": "${bottomPaneStyle}",
          "ButtonStyle": "${buttonStyle}",
          "TitleStyle": "${titleStyle}",
          "TextboxStyle": "${textboxStyle}",
          "ContainerWidth": "665",
          "HighlightColor": "${isDarkMode ? '#3b82f6' : '#ffff00'}",
          "IsDisplayTitle": true,
          "IsShowChartLinks": false,
          "IsShowEmbedButton": false,
          "CompactType": "large",
          "Calculator": "pip-value-calculator",
          "ContainerId": "pip-value-calculator-853433"
        });
      `
      document.head.appendChild(script2)
    }

    return () => {
      // Cleanup scripts when component unmounts
      const scripts = document.querySelectorAll('script[src*="fxverify.com"]')
      scripts.forEach(script => script.remove())
    }
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-950 dark:to-slate-900">
      {/* Header */}
      <div className="bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-b border-slate-200/50 dark:border-slate-700/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 rounded-2xl bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center">
                <Calculator className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Pip Value Calculator</h1>
                <p className="text-gray-600 dark:text-gray-400">Calculate pip values for different currency pairs</p>
              </div>
            </div>
            <Button className="bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-500 dark:to-cyan-500 text-white hover:from-blue-700 hover:to-cyan-700">
              Start Trading
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Calculator Container */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden border border-gray-200 dark:border-gray-700">
          {/* Calculator Header */}
          <div className="bg-gray-50 dark:bg-gray-900 px-6 py-6 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-3 mb-3">
              <Calculator className="w-8 h-8 text-blue-600 dark:text-blue-400" />
              Pip Value Calculator
            </h2>
            <p className="text-gray-600 dark:text-gray-300 text-lg leading-relaxed">
              Our advanced pip value calculator helps you determine the monetary value of each pip movement for different currency pairs. This tool is essential for proper position sizing and risk management in forex trading.
            </p>
          </div>

          {/* Embedded Calculator */}
          <div className="p-8 flex justify-center bg-white dark:bg-gray-800">
            <div id="pip-value-calculator-853433" className="w-full max-w-4xl"></div>
          </div>

          {/* Features Section */}
          <div className="bg-gray-50 dark:bg-gray-900 px-8 py-6 border-t border-gray-200 dark:border-gray-700">
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Calculator Features</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Calculator className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-1">Real-time Calculations</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-300">Instant pip value calculations for all major currency pairs</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Calculator className="w-4 h-4 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-1">Multiple Instruments</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-300">Support for forex pairs, indices, and commodities</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Calculator className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-1">Risk Management</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-300">Essential tool for proper position sizing and risk control</p>
                </div>
              </div>
            </div>
          </div>

          {/* Disclaimer */}
          <div className="bg-gray-100 dark:bg-gray-800 px-8 py-6 border-t border-gray-200 dark:border-gray-700">
            <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-3">Disclaimer</h4>
            <p className="text-base text-gray-700 dark:text-gray-300 leading-relaxed">
              The pip value calculator provides estimates based on current market conditions. Actual pip values may vary due to market fluctuations, spread changes, and broker-specific pricing. Always verify calculations with your broker before making trading decisions.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
} 