"use client"

export default function StructuredData() {
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "FxThrone",
    "description": "Professional proprietary trading firm offering simulated trading challenges and skill-based evaluations for educational purposes and performance-based funding opportunities.",
    "url": "https://fxthrone.com",
    "logo": "https://fxthrone.com/logo.png",
    "sameAs": [
      "https://twitter.com/fxthrone",
      "https://linkedin.com/company/fxthrone"
    ],
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "email": "<EMAIL>",
      "availableLanguage": ["English"]
    }
  }

  const serviceSchema = {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": "Trading Challenge Evaluation Program",
    "description": "Comprehensive prop firm evaluation using simulated trading accounts for educational purposes and skill-based assessment leading to performance-based funding opportunities.",
    "provider": {
      "@type": "Organization",
      "name": "FxThrone"
    },
    "serviceType": "Trading Challenge",
    "areaServed": "Worldwide",
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Trading Challenge Programs",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Standard Trading Challenge",
            "description": "Skill-based program using simulated trading accounts for educational purposes and performance-based assessment."
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Rapid Evaluation Program",
            "description": "Accelerated prop firm evaluation for experienced traders seeking faster trading skill assessment."
          }
        }
      ]
    }
  }

  const howToSchema = {
    "@context": "https://schema.org",
    "@type": "HowTo",
    "name": "How to Get Funded as a Forex Trader",
    "description": "Step-by-step guide to completing prop firm challenge requirements and accessing performance-based funding through simulated trading evaluations.",
    "step": [
      {
        "@type": "HowToStep",
        "name": "Start Your Trading Challenge",
        "text": "Choose from our skill-based programs designed for educational purposes, offering simulated trading accounts to suit different experience levels."
      },
      {
        "@type": "HowToStep",
        "name": "Complete Trading Skill Assessment",
        "text": "Demonstrate your trading skills through our performance-based assessment using simulated market conditions and trading evaluation criteria."
      },
      {
        "@type": "HowToStep",
        "name": "Access Funding Opportunity",
        "text": "Upon successful evaluation completion, gain access to performance-based funding opportunities through our trader funding program."
      }
    ]
  }

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationSchema)
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(serviceSchema)
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(howToSchema)
        }}
      />
    </>
  )
}
