"use client"

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { toast } from "sonner"
import { toastStyles } from "@/lib/utils"

// Types
interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  phone?: string
  country?: string
  avatar?: string
  role: 'trader' | 'admin'
  accountType: 'demo' | 'live' | 'funded'
  joinedAt: string
  lastLogin?: string
  isVerified: boolean
  kycStatus: 'pending' | 'approved' | 'rejected'
  tradingExperience: 'beginner' | 'intermediate' | 'advanced' | 'expert'
}

interface AuthContextType {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  isMockAuth: boolean
  login: (email: string, password: string) => Promise<void>
  signup: (userData: SignupData) => Promise<void>
  logout: () => void
  updateProfile: (data: Partial<User>) => Promise<void>
  refreshUser: () => Promise<void>
  getOrders: () => Promise<any[]>
  getUserProfile: () => Promise<any>
}

interface SignupData {
  firstName: string
  lastName: string
  email: string
  password: string
  phone?: string
  country?: string
  tradingExperience?: string
  username?: string
  address?: string
  referral_code?: string
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Mock user data for development
const mockUser: User = {
  id: "user_123",
  email: "<EMAIL>",
  firstName: "John",
  lastName: "Doe",
  phone: "+****************",
  country: "United States",
  avatar: "https://res.cloudinary.com/dufcjjaav/image/upload/v1752130069/placeholder-user.jpg",
  role: "trader",
  accountType: "funded",
  joinedAt: "2024-01-15T10:30:00Z",
  lastLogin: "2024-01-21T14:30:00Z",
  isVerified: true,
  kycStatus: "approved",
  tradingExperience: "intermediate"
}

// Provider component
export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // Check for existing session on mount
  useEffect(() => {
    checkAuthStatus()
  }, [])

  const checkAuthStatus = async () => {
    try {
      setIsLoading(true)
      
      // Check localStorage for auth token
      const token = localStorage.getItem('auth_token')
      const userData = localStorage.getItem('user_data')
      
      console.log('Auth check - token:', token ? 'found' : 'not found')
      console.log('Auth check - userData:', userData ? 'found' : 'not found')
      
      if (token && userData) {
        // Validate token with backend if it's a real token
        if (!token.startsWith('mock_jwt_token_')) {
          try {
            const response = await fetch('https://xthronebackend-9dbecc0e3719.herokuapp.com/auth/user/me', {
              method: 'GET',
              headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
              },
            })
            
            if (response.status === 401) {
              console.log('Token is invalid, clearing authentication')
              localStorage.removeItem('auth_token')
              localStorage.removeItem('user_data')
              setUser(null)
              return
            }
          } catch (error) {
            console.log('Token validation failed, but continuing with stored user data')
          }
        }
        
        // In a real app, you'd validate the token with your API
        const user = JSON.parse(userData)
        console.log('Setting user:', user)
        setUser(user)
      } else {
        console.log('No token or userData found')
      }
    } catch (error) {
      console.error('Auth check failed:', error)
      // Clear invalid data
      localStorage.removeItem('auth_token')
      localStorage.removeItem('user_data')
    } finally {
      setIsLoading(false)
    }
  }

  const login = async (email: string, password: string) => {
    try {
      setIsLoading(true)

      console.log('Attempting login with backend API...')
      console.log('Login data:', { username: email, password: '***' })

      // Create FormData for the login request
      const formData = new FormData()
      formData.append('username', email)  // Backend expects 'username' field
      formData.append('password', password)

      // Make real API call to your login endpoint
      const response = await fetch('https://xthronebackend-9dbecc0e3719.herokuapp.com/auth/login', {
        method: 'POST',
        body: formData,  // Send as FormData, not JSON
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error(`Login API failed: ${response.status} - ${errorText}`)

        if (response.status === 422) {
          console.error('Validation error - check if username/password format is correct')
          throw new Error('Invalid username or password format')
        }

        throw new Error(`Login failed: ${response.status} - ${errorText}`)
      }

      const data = await response.json()
      console.log('Login successful, received data:', {
        hasToken: !!(data.access_token || data.token),
        hasUser: !!data.user
      })

      // Store the real access token
      const token = data.access_token || data.token
      if (!token) {
        throw new Error('No access token received from login API')
      }

      localStorage.setItem('auth_token', token)
      localStorage.setItem('user_data', JSON.stringify(data.user || { ...mockUser, email }))

      setUser(data.user || { ...mockUser, email })
      console.log('Real authentication successful')

      // Show success toast with green styling
      toast.success("Login successful! Welcome back!", {
        description: "You have been successfully logged in to your account.",
        duration: 4000,
        style: toastStyles.success,
        icon: "🎉",
      })

    } catch (error) {
      console.error('Login failed:', error)
      
      // Show error toast
      toast.error("Login failed", {
        description: error instanceof Error ? error.message : "Please check your credentials and try again.",
        duration: 5000,
      })
      
      throw error // Remove mock fallback - force real authentication
    } finally {
      setIsLoading(false)
    }
  }

  const signup = async (userData: SignupData) => {
    try {
      setIsLoading(true)

      console.log('Attempting signup with backend API...')

      // Make real API call to your signup endpoint
      const response = await fetch('https://xthronebackend-9dbecc0e3719.herokuapp.com/auth/signup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: userData.username || userData.email.split('@')[0], // Use email prefix as username if not provided
          email: userData.email,
          password: userData.password,
          name: `${userData.firstName} ${userData.lastName}`,
          phone_no: userData.phone || '',
          country: userData.country || '',
          address: userData.address || '',
          referral_code: userData.referral_code || ''
        }),
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error(`Signup API failed: ${response.status} - ${errorText}`)
        throw new Error(`Signup failed: ${response.status}`)
      }

      const data = await response.json()
      console.log('Signup successful, received data:', data)

      // Don't automatically log in - user needs to verify email first
      console.log('Account created successfully - email verification required')

      // Show success toast with green styling
      toast.success("Account created successfully! 🎉", {
        description: `Welcome ${userData.firstName}! Please check your email to verify your account. You'll be redirected to login in a moment.`,
        duration: 3000,
        style: toastStyles.success,
        icon: "✨",
      })

    } catch (error) {
      console.error('Signup failed:', error)
      
      // Show error toast
      toast.error("Signup failed", {
        description: error instanceof Error ? error.message : "Please check your information and try again.",
        duration: 5000,
      })
      
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const logout = () => {
    // Clear storage
    localStorage.removeItem('auth_token')
    localStorage.removeItem('user_data')
    
    // Clear state
    setUser(null)
    
    // Redirect to home
    window.location.href = '/'
  }

  const handleAuthFailure = () => {
    console.log('Handling authentication failure - clearing session and redirecting to login')
    localStorage.removeItem('auth_token')
    localStorage.removeItem('user_data')
    setUser(null)
    
    // Redirect to login page
    window.location.href = '/auth'
  }

  const updateProfile = async (data: Partial<User>) => {
    try {
      setIsLoading(true)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      if (user) {
        const updatedUser = { ...user, ...data }
        
        // Update localStorage
        localStorage.setItem('user_data', JSON.stringify(updatedUser))
        
        setUser(updatedUser)
      }
    } catch (error) {
      console.error('Profile update failed:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const refreshUser = async () => {
    await checkAuthStatus()
  }

  const getOrders = async (): Promise<any[]> => {
    try {
      // Get the real access token from localStorage
      const token = localStorage.getItem('auth_token')

      console.log('Available localStorage keys:', Object.keys(localStorage))
      console.log('Token found:', token ? 'Yes' : 'No')
      console.log('Token value:', token)

      if (!token) {
        console.log('No token found - authentication required')
        throw new Error('No authentication token found. Please login first.')
      }

      // Remove mock token handling - only use real tokens

      // Use the real token with Bearer format
      const authHeader = `Bearer ${token}`
      console.log('Using real auth token for API call')

      const response = await fetch('https://xthronebackend-9dbecc0e3719.herokuapp.com/order/order_ids', {
        method: 'GET',
        headers: {
          'Authorization': authHeader,
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        console.error('API Response Status:', response.status)
        const responseText = await response.text()
        console.error('API Response Text:', responseText)

        // If unauthorized, the token might be expired or invalid
        if (response.status === 401) {
          console.error('Authentication failed - token may be expired or invalid')
          
          // Handle authentication failure
          handleAuthFailure()
          
          throw new Error('Authentication failed. Please login again.')
        }

        throw new Error(`API Error: ${response.status} - ${responseText}`)
      }

      const orders = await response.json()
      console.log('Orders fetched successfully from API:', orders)
      return orders
    } catch (error) {
      console.error('Error fetching orders:', error)
      throw error
    }
  }

  const getUserProfile = async (): Promise<any> => {
    try {
      // Get the real access token from localStorage
      const token = localStorage.getItem('auth_token')

      console.log('=== PROFILE API DEBUG (from auth context) ===')
      console.log('Token found:', token ? 'Yes' : 'No')
      console.log('Token preview:', token?.substring(0, 30) + '...')
      if (!token) {
        console.log('No token found - authentication required')
        throw new Error('No authentication token found. Please login first.')
      }

      // Remove mock token handling - only use real tokens

      // Try the real API with proper error handling
      const authHeader = `Bearer ${token}`
      console.log('Making API call to profile endpoint...')

      const response = await fetch('https://xthronebackend-9dbecc0e3719.herokuapp.com/auth/user/me', {
        method: 'GET',
        headers: {
          'Authorization': authHeader,
          'Content-Type': 'application/json',
        },
      })

      console.log('Profile API Response Status:', response.status)
      console.log('Profile API Response OK:', response.ok)

      if (!response.ok) {
        const responseText = await response.text()
        console.error('Profile API Error Response:', responseText)

        if (response.status === 401) {
          console.error('Authentication failed - token may be expired or invalid')
          
          // Handle authentication failure
          handleAuthFailure()
          
          throw new Error('Authentication failed. Please login again.')
        }

        throw new Error(`API Error: ${response.status} - ${responseText}`)
      }

      const profile = await response.json()
      console.log('Profile fetched successfully from API:', profile)
      return profile
    } catch (error) {
      console.error('Error fetching user profile:', error)
      throw error
    }
  }

  const isAuthenticated = !!user

  // Check if current authentication is using mock token
  const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null
  const isMockAuth = !!(token && token.startsWith('mock_jwt_token_'))

  console.log('Auth context - user:', user, 'isAuthenticated:', isAuthenticated, 'isMockAuth:', isMockAuth)

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    isMockAuth,
    login,
    signup,
    logout,
    updateProfile,
    refreshUser,
    getOrders,
    getUserProfile
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

// Hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Export types for use in other components
export type { User, AuthContextType, SignupData }
