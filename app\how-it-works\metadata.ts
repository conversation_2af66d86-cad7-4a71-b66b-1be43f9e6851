import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'How to Get Funded as a Forex Trader - Prop Firm Challenge Requirements',
  description: 'Learn how FxThrone\'s prop firm challenge requirements work. Discover our trading evaluation criteria and step-by-step process for performance-based funding through simulated accounts.',
  keywords: 'how to get funded as a forex trader, prop firm challenge requirements, trading evaluation criteria, performance-based funding, simulated trading account, trader funding program',
  openGraph: {
    title: 'How to Get Funded as a Forex Trader - Prop Firm Challenge Requirements',
    description: 'Learn how FxThrone\'s prop firm challenge requirements work. Discover our trading evaluation criteria and step-by-step process for performance-based funding through simulated accounts.',
    type: 'website',
    locale: 'en_US',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'How to Get Funded as a Forex Trader - Prop Firm Challenge Requirements',
    description: 'Learn how FxThrone\'s prop firm challenge requirements work. Discover our trading evaluation criteria and step-by-step process for performance-based funding through simulated accounts.',
  },
  robots: {
    index: true,
    follow: true,
  },
}
