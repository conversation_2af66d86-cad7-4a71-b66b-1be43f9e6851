"use client"

import { Bad<PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON>, Settings, X } from "lucide-react"
import { useState, useEffect } from "react"

export default function CookieConsent() {
  const [isVisible, setIsVisible] = useState(false)
  const [showDetails, setShowDetails] = useState(false)

  useEffect(() => {
    const consent = localStorage.getItem("cookie-consent")
    if (!consent) {
      setIsVisible(true)
    }
  }, [])

  const handleAcceptAll = () => {
    localStorage.setItem("cookie-consent", "all")
    setIsVisible(false)
  }

  const handleAcceptNecessary = () => {
    localStorage.setItem("cookie-consent", "necessary")
    setIsVisible(false)
  }

  const handleReject = () => {
    localStorage.setItem("cookie-consent", "rejected")
    setIsVisible(false)
  }

  if (!isVisible) return null

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 p-4 md:p-6">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-lg border border-gray-200 dark:border-gray-700 rounded-3xl p-6 md:p-8 shadow-2xl relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-cyan-500/5 dark:from-blue-500/10 dark:to-cyan-500/10" />

          <div className="relative z-10">
            <div className="flex items-start gap-4 mb-6">
              <div className="w-12 h-12 rounded-2xl bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center shadow-lg flex-shrink-0">
                <Cookie className="w-6 h-6 text-white" />
              </div>

              <div className="flex-grow">
                <div className="flex items-center gap-3 mb-3">
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white">Cookie Preferences</h3>
                  <Badge className="bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 text-xs">
                    <Shield className="w-3 h-3 mr-1" />
                    Privacy First
                  </Badge>
                </div>

                <p className="text-gray-700 dark:text-white/70 text-sm md:text-base leading-relaxed mb-4">
                  We use cookies to enhance your trading experience, provide personalized content, and analyze our
                  traffic. Your privacy is important to us, and you have full control over your cookie preferences.
                </p>

                {showDetails && (
                  <div className="bg-gray-50 dark:bg-white/5 rounded-2xl p-4 mb-4 space-y-3">
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 rounded-full bg-blue-500 mt-2 flex-shrink-0" />
                      <div>
                        <div className="font-semibold text-gray-900 dark:text-white text-sm">Essential Cookies</div>
                        <div className="text-xs text-gray-600 dark:text-white/60">
                          Required for basic site functionality and security
                        </div>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 rounded-full bg-green-500 mt-2 flex-shrink-0" />
                      <div>
                        <div className="font-semibold text-gray-900 dark:text-white text-sm">Analytics Cookies</div>
                        <div className="text-xs text-gray-600 dark:text-white/60">
                          Help us understand how you use our platform
                        </div>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 rounded-full bg-purple-500 mt-2 flex-shrink-0" />
                      <div>
                        <div className="font-semibold text-gray-900 dark:text-white text-sm">Marketing Cookies</div>
                        <div className="text-xs text-gray-600 dark:text-white/60">
                          Used to deliver relevant ads and track campaign performance
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <button
                onClick={() => setIsVisible(false)}
                className="w-8 h-8 rounded-full bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 flex items-center justify-center transition-colors duration-200 flex-shrink-0"
                aria-label="Close cookie consent"
              >
                <X className="w-4 h-4 text-gray-600 dark:text-gray-400" />
              </button>
            </div>

            <div className="flex flex-col sm:flex-row gap-3 items-center justify-between">
              <div className="flex flex-wrap gap-2">
                <Button
                  onClick={handleAcceptAll}
                  className="bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-500 dark:to-cyan-500 text-white hover:from-blue-700 hover:to-cyan-700 dark:hover:from-blue-600 dark:hover:to-cyan-600 px-6 py-2 text-sm font-semibold"
                >
                  Accept All
                </Button>

                <Button
                  onClick={handleAcceptNecessary}
                  variant="outline"
                  className="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 px-6 py-2 text-sm bg-transparent"
                >
                  Necessary Only
                </Button>

                <Button
                  onClick={() => setShowDetails(!showDetails)}
                  variant="ghost"
                  className="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 px-4 py-2 text-sm"
                >
                  <Settings className="w-4 h-4 mr-2" />
                  {showDetails ? "Hide" : "Customize"}
                </Button>
              </div>

              <Button
                onClick={handleReject}
                variant="ghost"
                className="text-gray-500 dark:text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 px-4 py-2 text-sm"
              >
                Reject All
              </Button>
            </div>

            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <p className="text-xs text-gray-500 dark:text-gray-500 text-center">
                By continuing to use our site, you agree to our{" "}
                <a href="/privacy" className="text-blue-600 dark:text-blue-400 hover:underline">
                  Privacy Policy
                </a>{" "}
                and{" "}
                <a href="/terms" className="text-blue-600 dark:text-blue-400 hover:underline">
                  Terms of Service
                </a>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
