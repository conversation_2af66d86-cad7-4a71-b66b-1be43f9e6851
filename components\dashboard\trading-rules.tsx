"use client"

import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Shield, AlertTriangle, CheckCircle, TrendingDown, Target, Calendar, Activity, Zap } from "lucide-react"
import { useLanguage } from "@/contexts/language-context"

export default function TradingRules() {
  const { t } = useLanguage()

  const ruleCompliance = [
    {
      id: 1,
      rule: t("rules.dailyLoss"),
      description: t("rules.dailyLossDesc"),
      current: -150,
      limit: -1250,
      remaining: 1100,
      status: "compliant",
      icon: TrendingDown,
      consequence: "Account termination",
    },
    {
      id: 2,
      rule: t("rules.maxLoss"),
      description: t("rules.maxLossDesc"),
      current: -800,
      limit: -2500,
      remaining: 1700,
      status: "compliant",
      icon: Shield,
      consequence: "Account termination",
    },
    {
      id: 3,
      rule: t("rules.profitTarget"),
      description: t("rules.profitTargetDesc"),
      current: 1850,
      limit: 2000,
      remaining: 150,
      status: "warning",
      icon: Target,
      consequence: "Phase completion",
    },
    {
      id: 4,
      rule: t("rules.tradingDays"),
      description: t("rules.tradingDaysDesc"),
      current: 3,
      limit: 4,
      remaining: 1,
      status: "warning",
      icon: Calendar,
      consequence: "Cannot request payout",
    },
    {
      id: 5,
      rule: t("rules.consistency"),
      description: t("rules.consistencyDesc"),
      current: 45,
      limit: 50,
      remaining: 5,
      status: "compliant",
      icon: Activity,
      consequence: "Rule violation",
    },
  ]

  const tradingPermissions = [
    {
      rule: t("rules.newsTrading"),
      description: t("rules.newsTradingDesc"),
      allowed: true,
      icon: Zap,
    },
    {
      rule: t("rules.weekendHolding"),
      description: t("rules.weekendHoldingDesc"),
      allowed: true,
      icon: Calendar,
    },
    {
      rule: t("rules.hedging"),
      description: t("rules.hedgingDesc"),
      allowed: false,
      icon: Shield,
    },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "compliant":
        return "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400"
      case "warning":
        return "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400"
      case "violation":
        return "bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400"
      default:
        return "bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-400"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "compliant":
        return <CheckCircle className="w-4 h-4" />
      case "warning":
        return <AlertTriangle className="w-4 h-4" />
      case "violation":
        return <AlertTriangle className="w-4 h-4" />
      default:
        return <CheckCircle className="w-4 h-4" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case "compliant":
        return t("rules.compliant")
      case "warning":
        return t("rules.warning")
      case "violation":
        return t("rules.violation")
      default:
        return status
    }
  }

  const calculateProgress = (current: number, limit: number) => {
    if (limit === 0) return 0
    return Math.min(Math.abs(current / limit) * 100, 100)
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">{t("rules.title")}</h1>
        <p className="text-gray-600 dark:text-gray-400">{t("rules.subtitle")}</p>
      </div>

      {/* Rule Compliance */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">{t("rules.compliance")}</h2>
        <div className="grid gap-6">
          {ruleCompliance.map((rule) => {
            const IconComponent = rule.icon
            const progress = calculateProgress(rule.current, rule.limit)

            return (
              <div
                key={rule.id}
                className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6"
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center">
                      <IconComponent className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{rule.rule}</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">{rule.description}</p>
                    </div>
                  </div>
                  <Badge className={getStatusColor(rule.status)}>
                    {getStatusIcon(rule.status)}
                    <span className="ml-1">{getStatusText(rule.status)}</span>
                  </Badge>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">{t("rules.current")}</p>
                    <p className="text-lg font-bold text-gray-900 dark:text-white">
                      {rule.current > 0 ? `+$${rule.current}` : `$${rule.current}`}
                    </p>
                  </div>
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">{t("rules.limit")}</p>
                    <p className="text-lg font-bold text-gray-900 dark:text-white">
                      {rule.limit > 0 ? `+$${rule.limit}` : `$${rule.limit}`}
                    </p>
                  </div>
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">{t("rules.remaining")}</p>
                    <p className="text-lg font-bold text-gray-900 dark:text-white">
                      {rule.remaining > 0 ? `+$${rule.remaining}` : `$${rule.remaining}`}
                    </p>
                  </div>
                </div>

                <div className="mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Usage: {progress.toFixed(1)}%
                    </span>
                  </div>
                  <Progress
                    value={progress}
                    className={`h-2 ${rule.status === "warning" ? "[&>div]:bg-yellow-500" : rule.status === "violation" ? "[&>div]:bg-red-500" : "[&>div]:bg-blue-500"}`}
                  />
                </div>

                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">
                    {t("rules.consequence")}: {rule.consequence}
                  </span>
                </div>
              </div>
            )
          })}
        </div>
      </div>

      {/* Trading Permissions */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">Trading Permissions</h2>
        <div className="grid gap-4">
          {tradingPermissions.map((permission, index) => {
            const IconComponent = permission.icon

            return (
              <div
                key={index}
                className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div
                      className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                        permission.allowed
                          ? "bg-gradient-to-r from-blue-500 to-cyan-500"
                          : "bg-gradient-to-r from-red-500 to-pink-500"
                      }`}
                    >
                      <IconComponent className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{permission.rule}</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">{permission.description}</p>
                    </div>
                  </div>
                  <Badge className={permission.allowed ? getStatusColor("compliant") : getStatusColor("violation")}>
                    {permission.allowed ? (
                      <>
                        <CheckCircle className="w-4 h-4" />
                        <span className="ml-1">{t("rules.allowed")}</span>
                      </>
                    ) : (
                      <>
                        <AlertTriangle className="w-4 h-4" />
                        <span className="ml-1">{t("rules.notAllowed")}</span>
                      </>
                    )}
                  </Badge>
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}
