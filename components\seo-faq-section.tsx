"use client"

import { useState } from "react"
import { ChevronDown, ChevronUp } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"

interface FAQItem {
  question: string
  answer: string
  category: string
}

const faqData: FAQItem[] = [
  {
    question: "What is a trading challenge?",
    answer: "A trading challenge is a skill-based program that uses simulated accounts for educational purposes. Traders demonstrate their abilities through performance-based assessment to potentially access funding opportunities.",
    category: "basics"
  },
  {
    question: "How does prop firm evaluation work?",
    answer: "Prop firm evaluation involves completing trading challenges using simulated trading accounts. The process includes meeting specific trading evaluation criteria and demonstrating consistent performance through our educational programs.",
    category: "evaluation"
  },
  {
    question: "What are the prop firm challenge requirements?",
    answer: "Our prop firm challenge requirements include meeting profit targets, staying within drawdown limits, and following risk management rules. All evaluations use simulated accounts for educational and assessment purposes.",
    category: "requirements"
  },
  {
    question: "How to get funded as a forex trader?",
    answer: "To get funded as a forex trader, complete our trading skill assessment through simulated trading challenges. Upon successful evaluation completion, you may access performance-based funding opportunities through our trader funding program.",
    category: "funding"
  },
  {
    question: "What is a simulated trading account?",
    answer: "A simulated trading account replicates real market conditions for educational purposes without using actual capital. These accounts are used in our skill-based programs for performance-based assessment and trader development.",
    category: "accounts"
  },
  {
    question: "What are the trading evaluation criteria?",
    answer: "Our trading evaluation criteria include profit targets, maximum drawdown limits, minimum trading days, and risk management compliance. All assessments are conducted through simulated accounts for educational purposes.",
    category: "criteria"
  }
]

export default function SEOFAQSection() {
  const [openItems, setOpenItems] = useState<number[]>([])

  const toggleItem = (index: number) => {
    setOpenItems(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    )
  }

  return (
    <section className="py-16 md:py-24 bg-gray-50 dark:bg-gray-900/50">
      <div className="max-w-4xl mx-auto px-8 md:px-12 lg:px-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-gray-900 dark:text-white">
            Frequently Asked Questions
          </h2>
          <p className="text-lg md:text-xl text-gray-700 dark:text-white/80 max-w-3xl mx-auto">
            Learn about our trading challenges, prop firm evaluation process, and how to get funded as a forex trader.
          </p>
        </div>

        <div className="space-y-4">
          {faqData.map((faq, index) => (
            <motion.div 
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <button
                onClick={() => toggleItem(index)}
                className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-300 cursor-pointer group"
              >
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white pr-4 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                  {faq.question}
                </h3>
                <motion.div
                  animate={{ rotate: openItems.includes(index) ? 180 : 0 }}
                  transition={{ duration: 0.3 }}
                  className="text-gray-500 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300"
                >
                  <ChevronDown className="w-5 h-5 flex-shrink-0" />
                </motion.div>
              </button>
              
              <AnimatePresence>
                {openItems.includes(index) && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: "auto", opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3, ease: "easeInOut" }}
                    className="overflow-hidden"
                  >
                    <div className="px-6 pb-4">
                      <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                        <p className="text-gray-700 dark:text-white/80 leading-relaxed">
                          {faq.answer}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          ))}
        </div>

        {/* Schema.org structured data for FAQ */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "FAQPage",
              "mainEntity": faqData.map(faq => ({
                "@type": "Question",
                "name": faq.question,
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": faq.answer
                }
              }))
            })
          }}
        />
      </div>
    </section>
  )
}
