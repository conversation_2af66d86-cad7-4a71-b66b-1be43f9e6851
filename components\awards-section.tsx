"use client"

import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Award, Star, Trophy, Medal, Crown, Shield } from "lucide-react"
import { useLanguage } from "@/contexts/language-context"

export default function AwardsSection() {
  const { t } = useLanguage()

  const awards = [
    {
      icon: Trophy,
      title: "Best Prop Trading Firm 2024",
      organization: "Trading Excellence Awards",
      year: "2024",
      color: "from-blue-500 to-cyan-500",
      bgColor: "from-blue-500/10 to-cyan-500/10",
      borderColor: "border-blue-200 dark:border-blue-400/20",
    },
    {
      icon: Crown,
      title: "Most Trusted Platform",
      organization: "Forex Industry Review",
      year: "2024",
      color: "from-indigo-500 to-purple-500",
      bgColor: "from-indigo-500/10 to-purple-500/10",
      borderColor: "border-indigo-200 dark:border-indigo-400/20",
    },
    {
      icon: Medal,
      title: "Excellence in Trading Technology",
      organization: "FinTech Innovation Awards",
      year: "2023",
      color: "from-cyan-500 to-teal-500",
      bgColor: "from-cyan-500/10 to-teal-500/10",
      borderColor: "border-cyan-200 dark:border-cyan-400/20",
    },
    {
      icon: Shield,
      title: "Best Risk Management",
      organization: "Risk Management Institute",
      year: "2023",
      color: "from-blue-600 to-indigo-600",
      bgColor: "from-blue-600/10 to-indigo-600/10",
      borderColor: "border-blue-200 dark:border-blue-400/20",
    },
    {
      icon: Star,
      title: "Top Rated by Traders",
      organization: "TradingView Community",
      year: "2024",
      color: "from-purple-500 to-pink-500",
      bgColor: "from-purple-500/10 to-pink-500/10",
      borderColor: "border-purple-200 dark:border-purple-400/20",
    },
    {
      icon: Award,
      title: "Innovation in Prop Trading",
      organization: "Global Trading Summit",
      year: "2023",
      color: "from-teal-500 to-blue-500",
      bgColor: "from-teal-500/10 to-blue-500/10",
      borderColor: "border-teal-200 dark:border-teal-400/20",
    },
  ]

  return (
    <section className="py-24 md:py-32 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-blue-50/30 to-transparent dark:via-blue-950/20" />
      <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-gradient-to-r from-blue-500/5 to-cyan-500/5 dark:from-blue-500/10 dark:to-cyan-500/10 rounded-full blur-3xl" />
      <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-indigo-500/5 to-purple-500/5 dark:from-indigo-500/10 dark:to-purple-500/10 rounded-full blur-3xl" />

      <div className="max-w-7xl mx-auto px-8 md:px-12 lg:px-16 relative">
        {/* Section Header */}
        <div className="text-center mb-20 md:mb-28">
          <Badge
            variant="outline"
            className="mb-6 text-sm font-light border-blue-200 dark:border-blue-400/20 text-blue-600 dark:text-blue-400 px-4 py-2"
          >
            <Award className="w-4 h-4 mr-2" />
            {t("awards.badge")}
          </Badge>

          <h2 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-8 leading-tight">
            <span className="bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-400 dark:to-cyan-400 bg-clip-text text-transparent">
              Award-Winning
            </span>{" "}
            <span className="text-gray-900 dark:text-white">Platform</span>
          </h2>

          <p className="text-lg md:text-xl lg:text-2xl text-gray-700 dark:text-white/70 max-w-3xl mx-auto leading-relaxed">
            Recognized globally for excellence in prop trading, technology innovation, and trader success.
          </p>
        </div>

        {/* Awards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 md:gap-10">
          {awards.map((award, index) => {
            const IconComponent = award.icon
            return (
              <Card
                key={index}
                className={`group relative overflow-hidden bg-gradient-to-br ${award.bgColor} dark:${award.bgColor.replace("/10", "/15")} backdrop-blur-sm border ${award.borderColor} shadow-sm hover:shadow-xl transition-all duration-500 hover:scale-105 hover:-rotate-1`}
              >
                <CardContent className="p-8 md:p-10">
                  {/* Award Icon */}
                  <div
                    className={`w-16 h-16 md:w-20 md:h-20 rounded-2xl bg-gradient-to-r ${award.color} flex items-center justify-center mb-6 md:mb-8 group-hover:scale-110 group-hover:rotate-12 transition-all duration-500 shadow-lg`}
                  >
                    <IconComponent className="w-8 h-8 md:w-10 md:h-10 text-white" />
                  </div>

                  {/* Award Details */}
                  <div className="space-y-3 md:space-y-4">
                    <h3 className="text-xl md:text-2xl font-bold text-gray-900 dark:text-white leading-tight group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                      {award.title}
                    </h3>

                    <p className="text-gray-600 dark:text-white/60 font-medium">{award.organization}</p>

                    <div className="flex items-center justify-between pt-2">
                      <Badge
                        variant="secondary"
                        className="bg-white/50 dark:bg-black/20 text-gray-700 dark:text-white/80 border-0"
                      >
                        {award.year}
                      </Badge>

                      <div className="flex items-center gap-1">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className="w-4 h-4 fill-yellow-400 text-yellow-400 group-hover:scale-110 transition-transform duration-300"
                            style={{ transitionDelay: `${i * 50}ms` }}
                          />
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Hover Effect Overlay */}
                  <div
                    className={`absolute inset-0 bg-gradient-to-br ${award.color} opacity-0 group-hover:opacity-5 transition-opacity duration-500 pointer-events-none`}
                  />
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Stats Section */}
        <div className="mt-20 md:mt-28 grid grid-cols-2 md:grid-cols-4 gap-8 md:gap-12">
          {[
            { number: "50M+", label: "Paid to Traders", color: "text-blue-600 dark:text-blue-400" },
            { number: "15K+", label: "Active Traders", color: "text-cyan-600 dark:text-cyan-400" },
            { number: "98%", label: "Satisfaction Rate", color: "text-indigo-600 dark:text-indigo-400" },
            { number: "24/7", label: "Support Available", color: "text-purple-600 dark:text-purple-400" },
          ].map((stat, index) => (
            <div key={index} className="text-center group">
              <div
                className={`text-3xl md:text-4xl lg:text-5xl font-bold ${stat.color} mb-2 md:mb-3 group-hover:scale-110 transition-transform duration-300`}
              >
                {stat.number}
              </div>
              <div className="text-gray-600 dark:text-white/60 font-medium">{stat.label}</div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
