"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronLeft, ChevronRight } from "lucide-react"
import Image from "next/image"
import { motion, AnimatePresence } from "framer-motion"

const certificates = [
  {
    id: 1,
    image: "https://res.cloudinary.com/dufcjjaav/image/upload/v1754116484/<PERSON>_<PERSON>wangi_FxThrone_Payout_Certificate_j2wsb7.jpg"
  },
  {
    id: 2,
    image: "https://res.cloudinary.com/dufcjjaav/image/upload/v1754116482/Hassan_Farah_FxThrone_Payout_Certificate_l7mojs.jpg"
  },
  {
    id: 3,
    image: "https://res.cloudinary.com/dufcjjaav/image/upload/v1754116484/<PERSON>_Mwangi_FxThrone_Payout_Certificate_j2wsb7.jpg"
  },
  {
    id: 4,
    image: "https://res.cloudinary.com/dufcjjaav/image/upload/v1754116482/<PERSON>_<PERSON><PERSON>_FxThrone_Payout_Certificate_l7mojs.jpg"
  },
  {
    id: 5,
    image: "https://res.cloudinary.com/dufcjjaav/image/upload/v1754116459/Imran_Tariq_FxThrone_Payout_Certificate_tiv0bj.jpg"
  }
]

export default function PayoutShowcase() {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying) return

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % certificates.length)
    }, 3000)

    return () => clearInterval(interval)
  }, [isAutoPlaying, certificates.length])

  const nextSlide = () => {
    setCurrentIndex((prev) => (prev + 1) % certificates.length)
    setIsAutoPlaying(false)
  }

  const prevSlide = () => {
    setCurrentIndex((prev) => (prev - 1 + certificates.length) % certificates.length)
    setIsAutoPlaying(false)
  }

  const goToSlide = (index: number) => {
    setCurrentIndex(index)
    setIsAutoPlaying(false)
  }

  return (
    <section className="py-20 bg-gradient-to-br from-blue-50 via-white to-cyan-50 dark:from-slate-900 dark:via-gray-900 dark:to-slate-800">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Certificate Carousel */}
        <div className="relative">
          {/* Navigation Buttons */}
          <motion.div
            initial={{ opacity: 0, x: -30, scale: 0.8 }}
            animate={{ opacity: 1, x: 0, scale: 1 }}
            transition={{ delay: 0.2, duration: 0.3, type: "spring", stiffness: 300 }}
            whileHover={{ scale: 1.1, x: -2 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button
              onClick={prevSlide}
              className="absolute left-4 top-1/2 -translate-y-1/2 z-10 w-12 h-12 rounded-full bg-white dark:bg-gray-800 shadow-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200"
              size="icon"
            >
              <ChevronLeft className="w-6 h-6" />
            </Button>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, x: 30, scale: 0.8 }}
            animate={{ opacity: 1, x: 0, scale: 1 }}
            transition={{ delay: 0.2, duration: 0.3, type: "spring", stiffness: 300 }}
            whileHover={{ scale: 1.1, x: 2 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button
              onClick={nextSlide}
              className="absolute right-4 top-1/2 -translate-y-1/2 z-10 w-12 h-12 rounded-full bg-white dark:bg-gray-800 shadow-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200"
              size="icon"
            >
              <ChevronRight className="w-6 h-6" />
            </Button>
          </motion.div>

          {/* Certificate Display */}
          <div className="max-w-4xl mx-auto">
            <div className="relative h-96 lg:h-[600px] overflow-hidden">
              <motion.div
                key={currentIndex}
                initial={{ opacity: 0, scale: 0.95, x: 100 }}
                animate={{ opacity: 1, scale: 1, x: 0 }}
                transition={{ 
                  duration: 0.3, 
                  ease: "easeOut",
                  type: "spring",
                  stiffness: 250,
                  damping: 20
                }}
                className="relative w-full h-full"
                whileHover={{ scale: 1.02 }}
              >
                <Image
                  src={certificates[currentIndex].image}
                  alt={`Payout Certificate ${currentIndex + 1}`}
                  fill
                  className="object-contain w-full h-full"
                  priority
                />
              </motion.div>
            </div>
          </div>

          {/* Dots Indicator */}
          <motion.div 
            className="flex justify-center mt-8 gap-2"
            initial={{ opacity: 0, y: 30, scale: 0.8 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ delay: 0.3, duration: 0.4, type: "spring", stiffness: 200 }}
          >
            {certificates.map((_, index) => (
              <motion.button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-200 ${
                  index === currentIndex
                    ? "bg-blue-600 dark:bg-blue-400"
                    : "bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500"
                }`}
                aria-label={`Go to slide ${index + 1}`}
                whileHover={{ scale: 1.4, y: -2 }}
                whileTap={{ scale: 0.8, y: 1 }}
                animate={{
                  scale: index === currentIndex ? 1.5 : 1,
                  y: index === currentIndex ? -3 : 0,
                  backgroundColor: index === currentIndex 
                    ? "rgb(37 99 235)" 
                    : "rgb(209 213 219)"
                }}
                transition={{ duration: 0.15, type: "spring", stiffness: 400 }}
              />
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  )
} 