"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Award, Download, Eye, CheckCircle, Clock, Lock } from "lucide-react"
import { useLanguage } from "@/contexts/language-context"

export default function Certificate() {
  const { t } = useLanguage()

  const availableCertificates = [
    {
      id: 1,
      title: t("certificate.phase1"),
      description: "Complete Phase 1 evaluation with 8% profit target",
      progress: 75,
      requirements: ["Achieve 8% profit target", "Maintain risk management", "Trade for minimum 4 days"],
      status: "in-progress",
      earnedDate: null,
      validUntil: null,
    },
    {
      id: 2,
      title: t("certificate.phase2"),
      description: "Complete Phase 2 evaluation with 5% profit target",
      progress: 0,
      requirements: ["Achieve 5% profit target", "Maintain risk management", "Trade for minimum 4 days"],
      status: "locked",
      earnedDate: null,
      validUntil: null,
    },
    {
      id: 3,
      title: t("certificate.funded"),
      description: "Successfully become a funded trader",
      progress: 0,
      requirements: ["Pass both evaluation phases", "Sign funding agreement", "Start live trading"],
      status: "locked",
      earnedDate: null,
      validUntil: null,
    },
  ]

  const earnedCertificates = [
    {
      id: 4,
      title: t("certificate.profitable"),
      description: "Achieved first profitable month",
      earnedDate: "2024-01-15",
      validUntil: "2025-01-15",
      status: "complete",
    },
    {
      id: 5,
      title: t("certificate.consistent"),
      description: "Maintained consistent performance for 3 months",
      earnedDate: "2024-02-20",
      validUntil: "2025-02-20",
      status: "complete",
    },
    {
      id: 6,
      title: t("certificate.riskManager"),
      description: "Demonstrated excellent risk management",
      earnedDate: "2024-03-10",
      validUntil: "2025-03-10",
      status: "complete",
    },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "complete":
        return "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400"
      case "in-progress":
        return "bg-cyan-100 dark:bg-cyan-900/30 text-cyan-700 dark:text-cyan-400"
      case "locked":
        return "bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-400"
      default:
        return "bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-400"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "complete":
        return <CheckCircle className="w-4 h-4" />
      case "in-progress":
        return <Clock className="w-4 h-4" />
      case "locked":
        return <Lock className="w-4 h-4" />
      default:
        return <Clock className="w-4 h-4" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case "complete":
        return t("certificate.complete")
      case "in-progress":
        return t("certificate.inProgress")
      case "locked":
        return t("certificate.locked")
      default:
        return t("certificate.locked")
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">{t("certificate.title")}</h1>
        <p className="text-gray-600 dark:text-gray-400">{t("certificate.subtitle")}</p>
      </div>

      {/* Available Certificates */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">{t("certificate.available")}</h2>
        <div className="grid gap-6">
          {availableCertificates.map((cert) => (
            <div
              key={cert.id}
              className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center">
                    <Award className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{cert.title}</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{cert.description}</p>
                  </div>
                </div>
                <Badge className={getStatusColor(cert.status)}>
                  {getStatusIcon(cert.status)}
                  <span className="ml-1">{getStatusText(cert.status)}</span>
                </Badge>
              </div>

              {cert.status === "in-progress" && (
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {t("certificate.progress")}
                    </span>
                    <span className="text-sm text-gray-600 dark:text-gray-400">{cert.progress}%</span>
                  </div>
                  <Progress value={cert.progress} className="h-2 [&>div]:bg-blue-500" />
                </div>
              )}

              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t("certificate.requirements")}
                </h4>
                <ul className="space-y-1">
                  {cert.requirements.map((req, index) => (
                    <li key={index} className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                      <div className="w-1.5 h-1.5 rounded-full bg-blue-400" />
                      {req}
                    </li>
                  ))}
                </ul>
              </div>

              <div className="flex gap-3">
                <Button
                  variant="outline"
                  size="sm"
                  disabled={cert.status === "locked"}
                  className="flex items-center gap-2 bg-transparent border-blue-200 dark:border-blue-400/20 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-500/10"
                >
                  <Eye className="w-4 h-4" />
                  {t("certificate.view")}
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Earned Certificates */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">{t("certificate.earned")}</h2>
        <div className="grid gap-6 md:grid-cols-2">
          {earnedCertificates.map((cert) => (
            <div
              key={cert.id}
              className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center">
                    <Award className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{cert.title}</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{cert.description}</p>
                  </div>
                </div>
                <Badge className={getStatusColor(cert.status)}>
                  {getStatusIcon(cert.status)}
                  <span className="ml-1">{getStatusText(cert.status)}</span>
                </Badge>
              </div>

              <div className="space-y-2 mb-4">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">{t("certificate.earnedOn")}:</span>
                  <span className="text-gray-900 dark:text-white">{cert.earnedDate}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">{t("certificate.validUntil")}:</span>
                  <span className="text-gray-900 dark:text-white">{cert.validUntil}</span>
                </div>
              </div>

              <div className="flex gap-3">
                <Button
                  size="sm"
                  className="flex items-center gap-2 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700"
                >
                  <Download className="w-4 h-4" />
                  {t("certificate.download")}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2 bg-transparent border-blue-200 dark:border-blue-400/20 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-500/10"
                >
                  <Eye className="w-4 h-4" />
                  {t("certificate.view")}
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
