"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft, Calendar, Globe } from "lucide-react"
import Link from "next/link"
import { useEffect, useRef, memo } from "react"
import Footer from "@/components/footer"

// TradingView Economic Calendar Widget Component
const TradingViewEconomicCalendar = memo(() => {
  const container = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (container.current) {
      const script = document.createElement("script")
      script.src = "https://s3.tradingview.com/external-embedding/embed-widget-events.js"
      script.type = "text/javascript"
      script.async = true
      script.innerHTML = `
        {
          "colorTheme": "light",
          "isTransparent": true,
          "locale": "en",
          "countryFilter": "",
          "importanceFilter": "-1,0,1",
          "width": 1022,
          "height": 464
        }`
      container.current.appendChild(script)
    }
  }, [])

  return (
    <div className="tradingview-widget-container" ref={container}>
      <div className="tradingview-widget-container__widget"></div>
      <div className="tradingview-widget-copyright">
        <a href="https://www.tradingview.com/economic-calendar/" rel="noopener nofollow" target="_blank">
          <span className="blue-text">Economic calendar by TradingView</span>
        </a>
      </div>
    </div>
  )
})

TradingViewEconomicCalendar.displayName = 'TradingViewEconomicCalendar'

export default function EconomicCalendarPage() {
  return (
    <main className="min-h-screen bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800">
      {/* Navigation */}
      <nav className="p-8">
        <Link href="/">
          <Button
            variant="ghost"
            className="text-gray-600 dark:text-white/70 hover:text-gray-900 dark:hover:text-white"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Button>
        </Link>
      </nav>

      {/* Page Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="w-12 h-12 rounded-2xl bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center">
              <Calendar className="w-6 h-6 text-white" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Economic Calendar</h1>
          </div>
          <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            Stay ahead with real-time economic events and market-moving announcements. Track important news that can impact your trading decisions.
          </p>
        </div>

        {/* Embedded Economic Calendar */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden border border-gray-200 dark:border-gray-700">
          {/* Calendar Header */}
          <div className="bg-gray-50 dark:bg-gray-900 px-6 py-6 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-3 mb-3">
              <Globe className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              Live Economic Events
            </h2>
            <p className="text-gray-600 dark:text-gray-300 text-lg leading-relaxed">
              Real-time economic calendar with comprehensive coverage of global market events, including interest rate decisions, employment reports, GDP releases, and more.
            </p>
          </div>

          {/* TradingView Economic Calendar Widget */}
          <div className="p-8 flex justify-center bg-white dark:bg-gray-800">
            <TradingViewEconomicCalendar />
          </div>

          {/* Features Section */}
          <div className="bg-gray-50 dark:bg-gray-900 px-8 py-6 border-t border-gray-200 dark:border-gray-700">
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Calendar Features</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Calendar className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-1">Real-time Updates</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-300">Live economic events with automatic updates</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Globe className="w-4 h-4 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-1">Global Coverage</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-300">Comprehensive coverage of all major economies</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Calendar className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-1">Impact Analysis</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-300">Clear impact levels for market volatility</p>
                </div>
              </div>
            </div>
          </div>

          {/* Market Impact Info */}
          <div className="bg-gray-100 dark:bg-gray-800 px-8 py-6 border-t border-gray-200 dark:border-gray-700">
            <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-3">Understanding Market Impact</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <span className="text-red-600 dark:text-red-400 font-bold">HIGH</span>
                </div>
                <h5 className="font-semibold text-gray-900 dark:text-white mb-1">High Impact</h5>
                <p className="text-xs text-gray-600 dark:text-gray-300">Major market movers causing significant volatility</p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <span className="text-yellow-600 dark:text-yellow-400 font-bold">MED</span>
                </div>
                <h5 className="font-semibold text-gray-900 dark:text-white mb-1">Medium Impact</h5>
                <p className="text-xs text-gray-600 dark:text-gray-300">Moderate influence with potential directional moves</p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <span className="text-blue-600 dark:text-blue-400 font-bold">LOW</span>
                </div>
                <h5 className="font-semibold text-gray-900 dark:text-white mb-1">Low Impact</h5>
                <p className="text-xs text-gray-600 dark:text-gray-300">Minor events with limited market influence</p>
              </div>
            </div>
          </div>

          {/* Disclaimer */}
          <div className="bg-gray-50 dark:bg-gray-900 px-8 py-6 border-t border-gray-200 dark:border-gray-700">
            <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-3">Disclaimer</h4>
            <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
              The economic calendar provides real-time data on economic events and their potential market impact. While we strive for accuracy, market conditions can change rapidly. Always verify information with your broker and consider professional advice before making trading decisions based on economic events.
            </p>
          </div>
        </div>
      </div>

      <Footer />
    </main>
  )
}
