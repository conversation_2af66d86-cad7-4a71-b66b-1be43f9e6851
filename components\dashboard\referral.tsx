"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  Lock,
  Copy,
  Users,
  Gift,
  TrendingUp,
  DollarSign,
  CheckCircle
} from "lucide-react"

export default function Referral() {
  // Simulate eligibility (replace with real logic)
  const [eligible] = useState(false) // set to true to test eligible state
  const [copied, setCopied] = useState(false)
  const referralCode = "BADAR123"
  const referrals: any[] = [] // replace with real data

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 text-white p-6 rounded-lg">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">Referral Program</h1>
            <p className="text-blue-100">Invite friends and earn rewards together</p>
          </div>
          <div className="flex items-center gap-6 text-sm">
            <div className="flex items-center gap-2">
              <Users className="w-4 h-4" />
              <span>0 Referrals</span>
            </div>
            <div className="flex items-center gap-2">
              <DollarSign className="w-4 h-4" />
              <span>$0.00 Earned</span>
            </div>
          </div>
        </div>
      </div>

      {/* Requirements Alert */}
      {!eligible && (
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
            <div className="flex items-start gap-4">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                <Lock className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Unlock Your Referral Code</h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  To access your referral code and start earning rewards, you need to:
                </p>
                <div className="space-y-2">
                  <div className="flex items-center gap-3">
                    <div className="w-6 h-6 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                      <span className="text-xs font-bold text-gray-600 dark:text-gray-400">1</span>
                    </div>
                    <span className="text-gray-700 dark:text-gray-300">Purchase an account</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-6 h-6 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                      <span className="text-xs font-bold text-gray-600 dark:text-gray-400">2</span>
                    </div>
                    <span className="text-gray-700 dark:text-gray-300">Trade for at least 7 days</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
      )}

      {/* Referral Code Section */}
      <div className="grid md:grid-cols-2 gap-6">
          {/* Left Column - Referral Code */}
          <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
            <CardHeader>
              <CardTitle className="text-gray-900 dark:text-white flex items-center gap-2">
                <Gift className="w-5 h-5 text-blue-600" />
                Your Referral Code
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-600 dark:text-gray-400 text-sm">
                Copy this code and share it with your friends.
              </p>

              {eligible ? (
                <div className="space-y-3">
                  <div className="relative">
                    <Input
                      value={referralCode}
                      readOnly
                      className="bg-gray-50 dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-center text-lg font-mono pr-12"
                    />
                    <Button
                      onClick={() => copyToClipboard(referralCode)}
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-blue-600 hover:bg-blue-700"
                      size="sm"
                    >
                      {copied ? <CheckCircle className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                    </Button>
                  </div>
                  <div className="text-green-600 dark:text-green-400 text-sm flex items-center gap-2">
                    <CheckCircle className="w-4 h-4" />
                    Code ready to share!
                  </div>
                </div>
              ) : (
                <div className="space-y-3">
                  <div className="relative">
                    <Input
                      value="••••••••"
                      readOnly
                      className="bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-400 dark:text-gray-500 text-center text-lg font-mono pr-12"
                    />
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <Lock className="w-4 h-4 text-gray-400 dark:text-gray-500" />
                    </div>
                  </div>
                  <div className="text-gray-600 dark:text-gray-400 text-sm flex items-center gap-2">
                    <Lock className="w-4 h-4" />
                    Complete requirements to unlock
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Right Column - Benefits */}
          <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
            <CardHeader>
              <CardTitle className="text-gray-900 dark:text-white flex items-center gap-2">
                <TrendingUp className="w-5 h-5 text-blue-600" />
                Referral Benefits
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-start gap-3 p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                    <Gift className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-1">For New Customers</h4>
                    <p className="text-gray-600 dark:text-gray-400 text-sm">Get 5% discount on their first purchase</p>
                  </div>
                </div>

                <div className="flex items-start gap-3 p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                    <DollarSign className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-1">For You</h4>
                    <p className="text-gray-600 dark:text-gray-400 text-sm">Receive 7% commission for each successful purchase</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
      </div>

      {/* Referrals Table */}
      <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
          <CardHeader>
            <CardTitle className="text-gray-900 dark:text-white flex items-center gap-2">
              <Users className="w-5 h-5 text-blue-600" />
              My Referrals
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead className="bg-gray-50 dark:bg-gray-700/50">
                  <tr>
                    <th className="px-6 py-4 text-left text-gray-700 dark:text-gray-300 font-medium">Serial</th>
                    <th className="px-6 py-4 text-left text-gray-700 dark:text-gray-300 font-medium">Name</th>
                    <th className="px-6 py-4 text-left text-gray-700 dark:text-gray-300 font-medium">Plan Purchased</th>
                    <th className="px-6 py-4 text-left text-gray-700 dark:text-gray-300 font-medium">Price</th>
                    <th className="px-6 py-4 text-left text-gray-700 dark:text-gray-300 font-medium">Commission</th>
                    <th className="px-6 py-4 text-left text-gray-700 dark:text-gray-300 font-medium">Commission Amount</th>
                    <th className="px-6 py-4 text-left text-gray-700 dark:text-gray-300 font-medium">Date</th>
                    <th className="px-6 py-4 text-left text-gray-700 dark:text-gray-300 font-medium">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {referrals.length === 0 ? (
                    <tr>
                      <td colSpan={8} className="text-center py-16">
                        <div className="flex flex-col items-center justify-center gap-4">
                          <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                            <Users className="w-8 h-8 text-gray-400 dark:text-gray-500" />
                          </div>
                          <div className="text-center">
                            <h3 className="text-gray-900 dark:text-gray-100 font-medium mb-2">No referrals yet</h3>
                            <p className="text-gray-500 dark:text-gray-400 text-sm">Start sharing your referral code to see your referrals here</p>
                          </div>
                        </div>
                      </td>
                    </tr>
                  ) : (
                    referrals.map((ref, idx) => (
                      <tr key={ref.id} className="border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors">
                        <td className="px-6 py-4 text-gray-700 dark:text-gray-300">{idx + 1}</td>
                        <td className="px-6 py-4 text-gray-900 dark:text-white font-medium">{ref.name}</td>
                        <td className="px-6 py-4 text-gray-700 dark:text-gray-300">{ref.plan}</td>
                        <td className="px-6 py-4 text-gray-700 dark:text-gray-300">{ref.price}</td>
                        <td className="px-6 py-4 text-gray-700 dark:text-gray-300">{ref.commission}</td>
                        <td className="px-6 py-4 text-green-600 dark:text-green-400 font-medium">{ref.commissionAmount}</td>
                        <td className="px-6 py-4 text-gray-700 dark:text-gray-300">{ref.date}</td>
                        <td className="px-6 py-4">
                          <Badge className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 border-green-200 dark:border-green-700">
                            {ref.status}
                          </Badge>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
    </div>
  )
}
