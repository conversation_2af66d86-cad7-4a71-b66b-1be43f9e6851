"use client"

import { Bad<PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"
import { Textarea } from "@/components/ui/textarea"
import {
  Shield,
  FileText,
  CheckCircle,
  Clock,
  AlertCircle,
  User,
  CreditCard,
  Camera,
  TrendingUp,
  Brain,
  Target,
  Award,
  ArrowRight,
  Upload,
  Eye,
  Lock,
} from "lucide-react"
import { useState } from "react"
import { useKYC } from "@/contexts/kyc-context"

export default function KYCVerification() {
  const { kycData, updateKYCStatus, updateEvaluationChallenge } = useKYC()
  const kycStatus = kycData.status

  const evaluationChallenges = [
    {
      id: 1,
      title: "Risk Management Assessment",
      description: "Demonstrate your understanding of risk management principles and position sizing",
      icon: Shield,
      status: "not_started", // 'not_started', 'in_progress', 'completed', 'failed'
      difficulty: "Intermediate",
      estimatedTime: "15 minutes",
      requirements: [
        "Calculate position sizes based on account balance",
        "Identify appropriate stop-loss levels",
        "Demonstrate risk-reward ratio understanding"
      ]
    },
    {
      id: 2,
      title: "Market Analysis Challenge",
      description: "Analyze market conditions and identify trading opportunities",
      icon: TrendingUp,
      status: "locked",
      difficulty: "Advanced",
      estimatedTime: "20 minutes",
      requirements: [
        "Technical analysis of provided charts",
        "Fundamental analysis interpretation",
        "Market sentiment evaluation"
      ]
    },
    {
      id: 3,
      title: "Trading Psychology Evaluation",
      description: "Assess your emotional control and decision-making under pressure",
      icon: Brain,
      status: "locked",
      difficulty: "Intermediate",
      estimatedTime: "10 minutes",
      requirements: [
        "Scenario-based decision making",
        "Emotional response assessment",
        "Discipline and patience evaluation"
      ]
    },
    {
      id: 4,
      title: "Compliance & Ethics Test",
      description: "Verify understanding of trading regulations and ethical practices",
      icon: Award,
      status: "locked",
      difficulty: "Beginner",
      estimatedTime: "12 minutes",
      requirements: [
        "Regulatory compliance knowledge",
        "Ethical trading practices",
        "Platform rules understanding"
      ]
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
      case "in_progress":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400"
      case "failed":
        return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
      case "locked":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"
      default:
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "Beginner":
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
      case "Intermediate":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"
      case "Advanced":
        return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">KYC Verification</h1>
        <p className="text-gray-600 dark:text-gray-300">
          Pass evaluation challenges to complete your KYC verification and unlock withdrawals
        </p>
      </div>

      {/* KYC Requirement Notice */}
      <Card className="bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
        <CardContent className="pt-6">
          <div className="flex items-start gap-4">
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
              <Target className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Evaluation Challenges Required</h3>
              <p className="text-gray-700 dark:text-gray-300 mb-4">
                To complete your KYC verification and unlock withdrawal capabilities, you must pass our trading
                evaluation challenges. These challenges test your trading knowledge, risk management skills,
                and understanding of market dynamics.
              </p>
              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3 mb-4">
                <div className="flex items-center gap-2 mb-2">
                  <AlertCircle className="w-4 h-4 text-yellow-600 dark:text-yellow-400" />
                  <span className="font-medium text-yellow-800 dark:text-yellow-300">Requirements:</span>
                </div>
                <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                  <li>• Pass all 4 evaluation challenges with minimum 80% score</li>
                  <li>• Complete challenges in the specified order</li>
                  <li>• Maximum 3 attempts per challenge</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Trading Evaluation Challenges */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            Trading Evaluation Challenges
          </CardTitle>
          <CardDescription>
            Complete these challenges to demonstrate your trading knowledge and skills
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6">
            {evaluationChallenges.map((challenge, index) => {
              const IconComponent = challenge.icon
              const isLocked = challenge.status === 'locked'
              const isCompleted = challenge.status === 'completed'

              return (
                <Card
                  key={challenge.id}
                  className={`transition-all duration-200 ${
                    isLocked ? 'opacity-60' : 'hover:shadow-lg'
                  } ${isCompleted ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800' : ''}`}
                >
                  <CardContent className="pt-6">
                    <div className="flex items-start gap-4">
                      <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                        isCompleted
                          ? 'bg-green-100 dark:bg-green-900/30'
                          : isLocked
                            ? 'bg-gray-100 dark:bg-gray-800'
                            : 'bg-blue-100 dark:bg-blue-900/30'
                      }`}>
                        {isCompleted ? (
                          <CheckCircle className="w-6 h-6 text-green-600 dark:text-green-400" />
                        ) : (
                          <IconComponent className={`w-6 h-6 ${
                            isLocked
                              ? 'text-gray-400'
                              : 'text-blue-600 dark:text-blue-400'
                          }`} />
                        )}
                      </div>

                      <div className="flex-1">
                        <div className="flex items-start justify-between mb-2">
                          <h3 className="font-semibold text-gray-900 dark:text-white">
                            {challenge.title}
                          </h3>
                          <div className="flex gap-2">
                            <Badge className={getStatusColor(challenge.status)}>
                              {challenge.status.replace('_', ' ')}
                            </Badge>
                            <Badge className={getDifficultyColor(challenge.difficulty)}>
                              {challenge.difficulty}
                            </Badge>
                          </div>
                        </div>

                        <p className="text-gray-600 dark:text-gray-300 mb-4">
                          {challenge.description}
                        </p>

                        <div className="flex items-center gap-4 mb-4 text-sm text-gray-500 dark:text-gray-400">
                          <span className="flex items-center gap-1">
                            <Clock className="w-4 h-4" />
                            {challenge.estimatedTime}
                          </span>
                        </div>

                        <div className="mb-4">
                          <h4 className="font-medium text-gray-900 dark:text-white mb-2">Requirements:</h4>
                          <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                            {challenge.requirements.map((req, reqIndex) => (
                              <li key={reqIndex} className="flex items-start gap-2">
                                <span className="text-blue-600 dark:text-blue-400 mt-1">•</span>
                                {req}
                              </li>
                            ))}
                          </ul>
                        </div>

                        <Button
                          disabled={isLocked || isCompleted}
                          onClick={() => {
                            if (!isLocked && !isCompleted) {
                              updateEvaluationChallenge(
                                challenge.id === 1 ? 'riskManagement' :
                                challenge.id === 2 ? 'marketAnalysis' :
                                challenge.id === 3 ? 'tradingPsychology' : 'complianceEthics',
                                'in_progress'
                              )
                            }
                          }}
                          className={`${
                            isCompleted
                              ? 'bg-green-600 hover:bg-green-700'
                              : 'bg-blue-600 hover:bg-blue-700'
                          } text-white`}
                        >
                          {isCompleted ? (
                            <>
                              <CheckCircle className="w-4 h-4 mr-2" />
                              Completed
                            </>
                          ) : isLocked ? (
                            <>
                              <Lock className="w-4 h-4 mr-2" />
                              Locked
                            </>
                          ) : (
                            <>
                              <Target className="w-4 h-4 mr-2" />
                              Start Challenge
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* KYC Status Information */}
      <Card className="bg-gray-50 dark:bg-gray-900/20 border-gray-200 dark:border-gray-800">
        <CardContent className="pt-6">
          <div className="flex items-start gap-4">
            <div className="w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
              <Shield className="w-6 h-6 text-gray-600 dark:text-gray-400" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Current KYC Status</h3>
              <div className="flex items-center gap-2 mb-3">
                <Badge className={getStatusColor(kycStatus)}>
                  {kycStatus.replace('_', ' ').toUpperCase()}
                </Badge>
              </div>
              <p className="text-gray-700 dark:text-gray-300 text-sm">
                {kycStatus === 'not_started' && "Complete the evaluation challenges above to begin your KYC verification process."}
                {kycStatus === 'in_progress' && "Your KYC verification is being reviewed. You'll receive an email notification once approved."}
                {kycStatus === 'completed' && "Congratulations! Your KYC verification is complete. You can now withdraw your profits."}
                {kycStatus === 'rejected' && "Your KYC verification was rejected. Please contact support for assistance."}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
