"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/sheet"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  TrendingUp,
  Gift,
  Moon,
  Sun,
  X,
  Copy,
  Clock,
  CreditCard,
  Bitcoin,
  ChevronDown,
  ChevronRight,
  Check,
  Minus,
  Plus,
  Loader2,
  Upload,
  QrCode,
  Receipt,
  Wallet,
  ExternalLink
} from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { useAuth } from "@/contexts/auth-context"
import { toast } from "sonner"
import { useRouter } from "next/navigation"

const challengeTypeData = {
  "1-phase": {
    name: "One-Step",
    accountSizes: [
      { size: "$1K", price: "10", discountedPrice: "8" },
      { size: "$3K", price: "17", discountedPrice: "13" },
      { size: "$5K", price: "24", discountedPrice: "19" },
      { size: "$10K", price: "42", discountedPrice: "33" },
      { size: "$25K", price: "73", discountedPrice: "58" },
      { size: "$50K", price: "138", discountedPrice: "55" },
      { size: "$100K", price: "193", discountedPrice: "77" },
      { size: "$200K", price: "263", discountedPrice: "105" },
      { size: "$500K", price: "438", discountedPrice: "175" }
    ]
  },
  "2-phase": {
    name: "Two-Step",
    accountSizes: [
      { size: "$1K", price: "8", discountedPrice: "6" },
      { size: "$3K", price: "14", discountedPrice: "11" },
      { size: "$5K", price: "22", discountedPrice: "17" },
      { size: "$10K", price: "32", discountedPrice: "25" },
      { size: "$25K", price: "62", discountedPrice: "49" },
      { size: "$50K", price: "105", discountedPrice: "42" },
      { size: "$100K", price: "163", discountedPrice: "65" },
      { size: "$200K", price: "225", discountedPrice: "90" },
      { size: "$500K", price: "338", discountedPrice: "135" }
    ]
  },
  "hft": {
    name: "HFT",
    accountSizes: [
      { size: "$1K", price: "15", discountedPrice: "12" },
      { size: "$3K", price: "28", discountedPrice: "22" },
      { size: "$5K", price: "43", discountedPrice: "34" },
      { size: "$10K", price: "64", discountedPrice: "51" },
      { size: "$25K", price: "112", discountedPrice: "89" },
      { size: "$50K", price: "213", discountedPrice: "85" },
      { size: "$100K", price: "288", discountedPrice: "115" },
      { size: "$200K", price: "420", discountedPrice: "168" },
      { size: "$500K", price: "675", discountedPrice: "270" }
    ]
  },
  "instant": {
    name: "Instant",
    accountSizes: [
      { size: "$1K", price: "56", discountedPrice: "45" },
      { size: "$3K", price: "106", discountedPrice: "85" },
      { size: "$5K", price: "169", discountedPrice: "135" },
      { size: "$10K", price: "244", discountedPrice: "195" },
      { size: "$25K", price: "482", discountedPrice: "385" },
      { size: "$50K", price: "938", discountedPrice: "375" },
      { size: "$100K", price: "1875", discountedPrice: "750" },
      { size: "$200K", price: "3125", discountedPrice: "1250" },
      { size: "$500K", price: "4625", discountedPrice: "1850" }
    ]
  }
} as const

export default function NewChallengePage() {
  const router = useRouter()
  const [selectedChallengeType, setSelectedChallengeType] = useState<keyof typeof challengeTypeData>("2-phase")
  const [isDarkMode, setIsDarkMode] = useState(false)
  const [isPaymentSheetOpen, setIsPaymentSheetOpen] = useState(false)
  const [selectedPlan, setSelectedPlan] = useState({
    size: "$25K",
    price: "155",
    discountedPrice: "124",
    platform: "MT5",
    swapType: "Swap"
  })
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState("")
  const [selectedPlatform, setSelectedPlatform] = useState("mt5")
  const [agreedToTerms, setAgreedToTerms] = useState(false)
  const [countdown, setCountdown] = useState({ hours: 85, minutes: 10, seconds: 54 })
  const [isSubmittingPayment, setIsSubmittingPayment] = useState(false)
  const [txid, setTxid] = useState("")
  const [uploadedImage, setUploadedImage] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [couponCode, setCouponCode] = useState("")
  const [isCouponApplied, setIsCouponApplied] = useState(false)
  const [finalPrice, setFinalPrice] = useState("")
  const [selectedCoupon, setSelectedCoupon] = useState("")
  const { user, isAuthenticated, logout } = useAuth()

  // Available coupons
  const availableCoupons = [
    {
      code: "SAVE50",
      name: "50% OFF",
      description: "Get 50% discount on your purchase",
      discount: 0.5,
      color: "from-green-500 to-emerald-500"
    },
    {
      code: "WELCOME30",
      name: "30% OFF",
      description: "Welcome discount for new users",
      discount: 0.3,
      color: "from-blue-500 to-cyan-500"
    }
  ]

  const challengeTypes = [
    {
      id: "2-phase",
      name: "2 Phase",
      active: selectedChallengeType === "2-phase"
    },
    {
      id: "1-phase",
      name: "1 Phase",
      active: selectedChallengeType === "1-phase"
    },
    { 
      id: "hft", 
      name: "HFT", 
      active: selectedChallengeType === "hft" 
    },
    { 
      id: "instant", 
      name: "Instant", 
      active: selectedChallengeType === "instant" 
    }
  ]

  // Payment addresses and QR codes
  const paymentInfo = {
    bitcoin: {
      address: "******************************************",
      qrCode: "/BTC.jpg",
      token: "BTC",
      logoUrl: "https://upload.wikimedia.org/wikipedia/commons/thumb/4/46/Bitcoin.svg/512px-Bitcoin.svg.png"
    },
    ethereum: {
      address: "******************************************",
      qrCode: "/Eth.jpg",
      token: "ETH",
      logoUrl: "https://upload.wikimedia.org/wikipedia/commons/thumb/6/6f/Ethereum-icon-purple.svg/512px-Ethereum-icon-purple.svg.png"
    },
    solana: {
      address: "E4uFLyjUT9Aemh3onuNpPLZWBxmfsrcWYcLvstQTS8Tv",
      qrCode: "/Solana.jpg",
      token: "SOL",
      logoUrl: "https://cryptologos.cc/logos/solana-sol-logo.png"
    },
    usdt: {
      address: "TQnR7p9cWJ2eDy4QQiqmLDw9GYTQr8zjHv",
      qrCode: "/Usdt trc20.jpg",
      token: "USDT",
      logoUrl: "https://cryptologos.cc/logos/tether-usdt-logo.png"
    },
    usdtPolygon: {
      address: "******************************************",
      qrCode: "/Usdt polygon.jpg",
      token: "USDT (Polygon)",
      logoUrl: "https://cryptologos.cc/logos/tether-usdt-logo.png"
    },
    usdtBep20: {
      address: "******************************************",
      qrCode: "/Usdt bep20.jpg",
      token: "USDT (BEP20)",
      logoUrl: "https://cryptologos.cc/logos/tether-usdt-logo.png"
    },
    usdtTrc20: {
      address: "TQnR7p9cWJ2eDy4QQiqmLDw9GYTQr8zjHv",
      qrCode: "/Usdt trc20.jpg",
      token: "USDT (TRC20)",
      logoUrl: "https://cryptologos.cc/logos/tether-usdt-logo.png"
    },
    bnb: {
      address: "******************************************",
      qrCode: "/placeholder.jpg",
      token: "BNB",
      logoUrl: "https://cryptologos.cc/logos/bnb-bnb-logo.png"
    },
    usdc: {
      address: "******************************************",
      qrCode: "/placeholder.jpg",
      token: "USDC",
      logoUrl: "https://cryptologos.cc/logos/usd-coin-usdc-logo.png"
    }
  }

  const applyCouponCode = (couponCode: string) => {
    const coupon = availableCoupons.find(c => c.code === couponCode)
    if (coupon) {
      setIsCouponApplied(true)
      setSelectedCoupon(couponCode)
      const originalPrice = parseInt(selectedPlan.price)
      const discountedPrice = Math.round(originalPrice * (1 - coupon.discount))
      setFinalPrice(discountedPrice.toString())
      toast.success(`${coupon.name} applied! ${Math.round(coupon.discount * 100)}% discount applied.`)
    } else {
      toast.error("Invalid coupon code. Please try again.")
    }
  }

  const removeCoupon = () => {
    setIsCouponApplied(false)
    setSelectedCoupon("")
    setFinalPrice(selectedPlan.discountedPrice)
    toast.success("Coupon removed")
  }

  const copyAddress = (address: string) => {
    navigator.clipboard.writeText(address)
    toast.success("Address copied to clipboard!")
  }

  const getTradingParameters = (challengeType: string) => {
    switch (challengeType) {
      case "1-phase":
        return [
          { parameter: "Profit Target", value: "10%" },
          { parameter: "Daily Drawdown Limit", value: "4%" },
          { parameter: "Overall Drawdown Limit", value: "8%" },
          { parameter: "Minimum Trading Days", value: "5" },
          { parameter: "Leverage", value: "1:100" },
          { parameter: "Profit Split", value: "80%" },
          { parameter: "News Trading", value: "Allowed" },
          { parameter: "Overnight Trading", value: "Allowed" },
          { parameter: "Weekend Holding", value: "Allowed" },
          { parameter: "Consistency Rule", value: "None" },
          { parameter: "Withdrawals", value: "Weekly" },
          { parameter: "EA (Expert Advisor)", value: "Allowed" },
          { parameter: "HFT", value: "Not Allowed" }
        ]
      case "2-phase":
        return [
          { parameter: "Profit Target (Phase 1)", value: "10%" },
          { parameter: "Profit Target (Phase 2)", value: "5%" },
          { parameter: "Daily Drawdown Limit", value: "5%" },
          { parameter: "Overall Drawdown Limit", value: "10%" },
          { parameter: "Minimum Trading Days", value: "5" },
          { parameter: "Leverage", value: "1:100" },
          { parameter: "Profit Split", value: "80%" },
          { parameter: "News Trading", value: "Allowed" },
          { parameter: "Overnight Trading", value: "Allowed" },
          { parameter: "Weekend Holding", value: "Allowed" },
          { parameter: "Consistency Rule", value: "None" },
          { parameter: "Withdrawals", value: "Weekly" },
          { parameter: "EA (Expert Advisor)", value: "Allowed" },
          { parameter: "HFT", value: "Not Allowed" }
        ]
      case "hft":
        return [
          { parameter: "Profit Target", value: "8%" },
          { parameter: "Daily Drawdown Limit", value: "3%" },
          { parameter: "Overall Drawdown Limit", value: "6%" },
          { parameter: "Minimum Trading Days", value: "1" },
          { parameter: "Leverage", value: "1:100" },
          { parameter: "Profit Split", value: "90%" },
          { parameter: "News Trading", value: "Allowed" },
          { parameter: "Overnight Trading", value: "Allowed" },
          { parameter: "Weekend Holding", value: "Not Allowed" },
          { parameter: "Consistency Rule", value: "35% Profit Consistency Rule" },
          { parameter: "Withdrawals", value: "Bi-Weekly" },
          { parameter: "HFT Bot", value: "Allowed" }
        ]
      case "instant":
        return [
          { parameter: "Daily Drawdown Limit", value: "2%" },
          { parameter: "Overall Drawdown Limit", value: "4%" },
          { parameter: "Leverage", value: "1:100" },
          { parameter: "Profit Split", value: "90%" },
          { parameter: "News Trading", value: "Not Allowed" },
          { parameter: "Overnight Trading", value: "Allowed" },
          { parameter: "Weekend Holding", value: "Not Allowed" },
          { parameter: "Consistency Rule", value: "25% Profit Consistency Rule" },
          { parameter: "Withdrawals", value: "Bi-Weekly" },
          { parameter: "EA/HFT", value: "Not Allowed" }
        ]
      default:
        return [
          { parameter: "Profit Target", value: "10%" },
          { parameter: "Daily Drawdown Limit", value: "4%" },
          { parameter: "Overall Drawdown Limit", value: "8%" },
          { parameter: "Minimum Trading Days", value: "5" },
          { parameter: "Leverage", value: "1:100" },
          { parameter: "Profit Split", value: "80%" },
          { parameter: "News Trading", value: "Allowed" },
          { parameter: "Overnight Trading", value: "Allowed" },
          { parameter: "Weekend Holding", value: "Allowed" },
          { parameter: "Consistency Rule", value: "None" },
          { parameter: "Withdrawals", value: "Weekly" },
          { parameter: "EA (Expert Advisor)", value: "Allowed" },
          { parameter: "HFT", value: "Not Allowed" }
        ]
    }
  }

  const paymentMethods = [
    {
      id: "cards",
      name: "International Cards",
      icon: CreditCard,
      description: "Visa, Mastercard, American Express",
      fees: "0%"
    },
    {
      id: "skrill",
      name: "Skrill",
      icon: CreditCard,
      description: "Skrill",
      fees: "+3% Provider Fees"
    },
    {
      id: "crypto",
      name: "Crypto",
      icon: Bitcoin,
      description: "Bitcoin, Ethereum, USDT",
      fees: "+3% Provider Fees"
    }
  ]

  const platforms = [
    {
      id: "mt5",
      name: "MT5",
      description: "MetaTrader 5",
      logoUrl: "https://res.cloudinary.com/dufcjjaav/image/upload/v1752130069/MT5-removebg-preview_1_lqagz7.png",
      popular: true,
    },
    {
      id: "mt4",
      name: "MT4",
      description: "MetaTrader 4",
      logoUrl: "https://res.cloudinary.com/dufcjjaav/image/upload/v1752130069/512x512bb-removebg-preview_qnfrll.png",
      popular: false,
    },
  ]

  const cryptoPayments = [
    {
      id: "bitcoin",
      name: "Bitcoin",
      symbol: "BTC",
      logoUrl: "https://s2.coinmarketcap.com/static/img/coins/64x64/1.png",
    },
    {
      id: "ethereum",
      name: "Ethereum",
      symbol: "ETH",
      logoUrl: "https://s2.coinmarketcap.com/static/img/coins/64x64/1027.png",
    },
    {
      id: "solana",
      name: "Solana",
      symbol: "SOL",
      logoUrl: "https://s2.coinmarketcap.com/static/img/coins/64x64/48543.png",
    },
    {
      id: "usdt",
      name: "Tether",
      symbol: "USDT",
      logoUrl: "https://s2.coinmarketcap.com/static/img/coins/64x64/825.png",
    },
    {
      id: "usdtPolygon",
      name: "USDT (Polygon)",
      symbol: "USDT",
      logoUrl: "https://s2.coinmarketcap.com/static/img/coins/64x64/825.png",
    },
    {
      id: "usdtBep20",
      name: "USDT (BEP20)",
      symbol: "USDT",
      logoUrl: "https://s2.coinmarketcap.com/static/img/coins/64x64/825.png",
    },
    {
      id: "usdtTrc20",
      name: "USDT (TRC20)",
      symbol: "USDT",
      logoUrl: "https://s2.coinmarketcap.com/static/img/coins/64x64/825.png",
    },
    {
      id: "bnb",
      name: "BNB",
      symbol: "BNB",
      logoUrl: "https://s2.coinmarketcap.com/static/img/coins/64x64/1839.png",
    },
    {
      id: "usdc",
      name: "USD Coin",
      symbol: "USDC",
      logoUrl: "https://s2.coinmarketcap.com/static/img/coins/64x64/3408.png",
    },
  ]

  const addOns = [
    {
      id: "lifetime",
      name: "Lifetime Payout 95%",
      price: "+30%",
      description: "Get 95% profit split for life"
    },
    {
      id: "no-minimum",
      name: "No Minimum Trading Days",
      price: "+20%",
      description: "Remove minimum trading day requirement"
    },
    {
      id: "bi-weekly",
      name: "Bi-Weekly Payout",
      price: "+15%",
      description: "Get paid every 2 weeks"
    },
    {
      id: "overall-loss",
      name: "Overall Loss Limit 10%",
      price: "+25%",
      description: "Increase overall loss limit to 10%"
    },
    {
      id: "double-up",
      name: "Double Up",
      price: "+40%",
      description: "Double your account size"
    }
  ]

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode)
    document.documentElement.classList.toggle('dark')
  }

  const handleBuyNow = (size: string, price: string, discountedPrice: string) => {
    const selectedPlatformData = platforms.find(p => p.id === selectedPlatform)
    setSelectedPlan({
      ...selectedPlan,
      size,
      price,
      discountedPrice,
      platform: selectedPlatformData?.name || "MT5"
    })
    setFinalPrice(discountedPrice)
    setIsCouponApplied(false)
    setSelectedCoupon("")
    setIsPaymentSheetOpen(true)
  }

  const copyCouponCode = () => {
    navigator.clipboard.writeText('LITE5%')
  }

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setUploadedImage(file)
      const reader = new FileReader()
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const handleSubmitPayment = async () => {
    if (!isAuthenticated) {
      toast.error("Please login to submit payment")
      return
    }

    if (!txid.trim()) {
      toast.error("Please enter the transaction ID")
      return
    }

    if (!uploadedImage) {
      toast.error("Please upload a payment confirmation image")
      return
    }

    setIsSubmittingPayment(true)
    
    try {
      // Get the real access token from localStorage
      const token = localStorage.getItem('auth_token')

      if (!token) {
        toast.error("No authentication token found. Please login first.")
        return
      }

      // Create FormData for the payment submission
      const formData = new FormData()
      formData.append('txid', txid.trim())
      formData.append('payment_image', uploadedImage)
      formData.append('account_size', selectedPlan.size.replace(/[$,]/g, ''))
      formData.append('challenge_type', selectedChallengeType)
      formData.append('platform', selectedPlan.platform)
      formData.append('payment_method', selectedPaymentMethod)

      const authHeader = `Bearer ${token}`

      const response = await fetch('https://xthronebackend-9dbecc0e3719.herokuapp.com/order/order', {
        method: 'POST',
        headers: {
          'Authorization': authHeader,
        },
        body: formData,
      })

      if (!response.ok) {
        const responseText = await response.text()
        throw new Error(`Failed to submit payment: ${response.status} - ${responseText}`)
      }

      const result = await response.json()
      console.log('Payment submitted successfully:', result)
      
      toast.success("Payment submitted successfully! We'll verify and process your order.")
      setIsPaymentSheetOpen(false)
      
      // Reset form
      setTxid("")
      setUploadedImage(null)
      setImagePreview(null)
      
      // Redirect to dashboard overview page
      setTimeout(() => {
        router.push('/dashboard/overview')
      }, 1500)
      
    } catch (error) {
      console.error('Error submitting payment:', error)
      toast.error(error instanceof Error ? error.message : "Failed to submit payment. Please try again.")
    } finally {
      setIsSubmittingPayment(false)
    }
  }

  // Generate QR code data (you can replace this with actual payment address)
  const generateQRCodeData = () => {
    const selectedCrypto = cryptoPayments.find(crypto => crypto.id === selectedPaymentMethod)
    if (!selectedCrypto) return ""
    
    // This is a placeholder - replace with actual payment address
    return `bitcoin:**********************************?amount=${selectedPlan.discountedPrice}`
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-slate-950 dark:via-gray-900 dark:to-slate-950 text-gray-900 dark:text-white overflow-hidden relative transition-colors duration-500">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-400/10 dark:bg-blue-400/5 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-cyan-400/10 dark:bg-cyan-400/5 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-indigo-400/5 dark:bg-indigo-400/3 rounded-full blur-3xl"></div>
      </div>

      {/* Navigation */}
      <nav className="fixed top-4 left-4 right-4 z-40 flex items-center justify-between bg-transparent backdrop-blur-sm border border-gray-200/30 dark:border-white/10 rounded-2xl px-3 py-2 shadow-sm">
        <Link href="/" className="flex items-center gap-2 group">
          <div className="w-8 h-8 md:w-10 md:h-10 flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
            <Image
              src="https://res.cloudinary.com/dufcjjaav/image/upload/v1754115839/180_3_3_ygapel.png"
              alt="Forex Throne Logo"
              width={40}
              height={40}
              className="w-full h-full object-contain"
              priority
            />
          </div>
          <div className="hidden sm:block">
            <span className="text-lg md:text-xl font-bold bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent">
              FxThrone
            </span>
          </div>
        </Link>

        <div className="flex items-center gap-2 md:gap-4">
          <Button
            variant="ghost"
            onClick={toggleTheme}
            className="text-xs md:text-sm font-light text-gray-700 dark:text-white/80 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/30 dark:hover:bg-white/5 transition-all duration-300 px-2 py-1 rounded-lg group"
          >
            <div className="group-hover:rotate-180 transition-transform duration-500">
              {isDarkMode ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
            </div>
          </Button>
          <Link href="/dashboard">
            <Button variant="ghost" className="text-xs md:text-sm font-medium text-gray-700 dark:text-white/80 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/30 dark:hover:bg-white/5 transition-all duration-300 px-3 py-2 rounded-lg">
              Dashboard
            </Button>
          </Link>
        </div>
      </nav>

      {/* Main Content */}
      <main className="relative z-10 pt-24">
        <section className="py-16 md:py-24">
          <div className="max-w-7xl mx-auto px-8 md:px-12 lg:px-16 relative z-10">
            {/* Header */}
            <div className="text-center mb-8">
              <h2 className="text-xl md:text-2xl lg:text-3xl font-bold mb-3 leading-tight tracking-tight">
                Choose Your
                <span className="block bg-gradient-to-r from-blue-600 via-cyan-600 to-indigo-600 dark:from-blue-400 dark:via-cyan-400 dark:to-indigo-400 bg-clip-text text-transparent">
                  Challenge
                </span>
              </h2>

              {/* Coupon Code Advertisement */}
              <div className="max-w-2xl mx-auto mb-4">
                <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border-2 border-green-200 dark:border-green-700 rounded-2xl p-3 shadow-lg">
                  <div className="flex items-center justify-center gap-1 mb-1">
                    <Gift className="w-4 h-4 text-green-600" />
                    <span className="text-xs font-bold text-green-700 dark:text-green-400">LIMITED TIME OFFER!</span>
                  </div>
                  <p className="text-gray-700 dark:text-gray-300 mb-2 text-xs">
                    Get 50% OFF on all challenges! Use coupon code at checkout.
                  </p>
                  <div className="bg-white dark:bg-gray-800 border-2 border-dashed border-green-300 dark:border-green-600 rounded-lg p-1 inline-block">
                    <div className="flex items-center gap-1">
                      <span className="text-xs text-gray-600 dark:text-gray-400">Coupon Code:</span>
                      <span className="text-sm font-bold text-green-600 dark:text-green-400 tracking-wider">SAVE50</span>
                      <Button
                        size="sm"
                        variant="outline"
                        className="ml-1 text-xs border-green-300 text-green-600 hover:bg-green-50 dark:border-green-600 dark:text-green-400 dark:hover:bg-green-900/20"
                        onClick={() => navigator.clipboard.writeText('SAVE50')}
                      >
                        Copy
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Challenge Type Selectors */}
            <div className="flex flex-col items-center mb-8">
              <div className="bg-white dark:bg-gray-900 rounded-2xl p-2 md:p-3 shadow-lg border border-gray-200 dark:border-gray-700 mb-4 w-full max-w-4xl">
                {/* Challenge Options Row */}
                <div className="flex items-center justify-center gap-1 md:gap-2 mb-3 flex-wrap">
                  {challengeTypes.map((type) => (
                    <div key={type.id} className="flex flex-col items-center">
                      <Button
                        variant={type.active ? "default" : "ghost"}
                        className={`rounded-xl px-1 md:px-3 py-1 text-xs font-semibold transition-all duration-300 ${
                          type.active
                            ? "bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg hover:from-blue-700 hover:to-cyan-700"
                            : "text-gray-600 dark:text-white/70 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800"
                        }`}
                        onClick={() => {
                          setSelectedChallengeType(type.id as keyof typeof challengeTypeData)
                        }}
                      >
                        {type.name}
                      </Button>
                    </div>
                  ))}
                </div>
              </div>

              <Button className="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white px-3 md:px-4 py-1 md:py-2 rounded-xl text-xs font-semibold hover:scale-105 transition-all duration-300 shadow-lg">
                Get Funded
              </Button>
            </div>

            {/* Platform Selection */}
            <div className="mb-8">
              <div className="text-center mb-4">
                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">Choose Trading Platform</h3>
              </div>

              <div className="flex justify-center gap-4 max-w-md mx-auto">
                {platforms.map((platform) => (
                  <div
                    key={platform.id}
                    className={`cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-105 ${
                      selectedPlatform === platform.id
                        ? "ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-900/20 shadow-lg"
                        : "hover:bg-gray-50 dark:hover:bg-gray-800"
                    } bg-white dark:bg-gray-900 rounded-xl p-3 border border-gray-200 dark:border-gray-700 relative overflow-hidden`}
                    onClick={() => setSelectedPlatform(platform.id)}
                  >
                    {platform.popular && (
                      <div className="absolute -top-1 -right-1 bg-green-500 text-white text-xs font-bold px-1 py-0.5 rounded-full">
                        ✓
                      </div>
                    )}

                    <div className="text-center">
                      <div className="w-12 h-12 mx-auto mb-2 flex items-center justify-center">
                        <Image
                          src={platform.logoUrl}
                          alt={`${platform.name} logo`}
                          width={40}
                          height={40}
                          className="w-10 h-10 object-contain"
                        />
                      </div>
                      <h4 className="font-bold text-gray-900 dark:text-white text-sm">{platform.name}</h4>
                      <p className="text-xs text-gray-600 dark:text-gray-400">{platform.description}</p>
                    </div>

                    {selectedPlatform === platform.id && (
                      <div className="absolute bottom-1 right-1">
                        <Check className="w-4 h-4 text-green-500" />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Pricing Table */}
            <div className="bg-white dark:bg-gray-900 rounded-3xl shadow-2xl overflow-hidden border border-gray-200 dark:border-gray-700">
              <div className="overflow-x-auto">
                <table className="w-full min-w-[600px]">
                  <thead>
                    <tr className="bg-gradient-to-r from-blue-100 to-cyan-100 dark:from-blue-900/20 dark:to-cyan-900/20">
                      <th className="text-left p-2 md:p-3 font-bold text-gray-900 dark:text-white text-xs">
                        Trading Parameters
                      </th>
                      {challengeTypeData[selectedChallengeType]?.accountSizes.map((account, index) => (
                        <th key={index} className="text-center p-2 md:p-3 font-bold text-gray-900 dark:text-white text-xs">
                          {account.size}
                        </th>
                      ))}
                    </tr>
                    {/* Buy Now Buttons Row */}
                    <tr className="bg-white dark:bg-gray-900">
                      <td className="p-2 md:p-3 font-bold text-gray-900 dark:text-white text-xs">
                        Buy Now
                      </td>
                      {challengeTypeData[selectedChallengeType]?.accountSizes.map((account, index) => (
                        <td key={index} className="text-center p-2 md:p-3">
                          <Button 
                            className="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white px-2 md:px-3 py-1 rounded-xl text-xs font-semibold transition-all duration-300 hover:scale-105 shadow-md"
                            onClick={() => handleBuyNow(account.size, account.price, account.discountedPrice)}
                          >
                            Buy Now
                          </Button>
                        </td>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {getTradingParameters(selectedChallengeType).map((param, index) => (
                      <tr
                        key={index}
                        className={`border-b border-gray-100 dark:border-gray-800 ${
                          index % 2 === 0 ? "bg-gray-50 dark:bg-gray-800/50" : ""
                        }`}
                      >
                        <td className="p-2 md:p-3 font-semibold text-gray-900 dark:text-white text-xs">
                          {param.parameter}
                        </td>
                        {challengeTypeData[selectedChallengeType]?.accountSizes.map((_, accountIndex) => (
                          <td key={accountIndex} className="text-center p-2 md:p-3 text-gray-700 dark:text-white/70 text-xs font-medium">
                            {param.value}
                          </td>
                        ))}
                      </tr>
                    ))}
                    {/* Price Row */}
                    <tr className="border-t-2 border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20">
                      <td className="p-2 md:p-3 font-bold text-gray-900 dark:text-white text-xs">
                        Price
                      </td>
                      {challengeTypeData[selectedChallengeType]?.accountSizes.map((account, index) => (
                        <td key={index} className="text-center p-2 md:p-3">
                          <div className="text-sm font-bold text-blue-600 dark:text-blue-400">
                            ${account.discountedPrice}
                          </div>
                          <div className="text-xs text-gray-500 line-through">
                            ${account.price}
                          </div>
                        </td>
                      ))}
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Payment Side Panel */}
      <Sheet open={isPaymentSheetOpen} onOpenChange={setIsPaymentSheetOpen}>
        <SheetContent side="right" className="w-full sm:max-w-md lg:max-w-lg xl:max-w-xl overflow-y-auto">
          <div className="h-full flex flex-col">
            {/* Header */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 rounded-lg overflow-hidden">
                  <img 
                    src="/placeholder-logo.png" 
                    alt="FxThrone Logo" 
                    className="w-full h-full object-cover"
                  />
                </div>
                <div>
                  <h2 className="text-sm font-bold text-gray-900 dark:text-white">
                    Payment Confirmation
                  </h2>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    {challengeTypeData[selectedChallengeType]?.name} Challenge - {selectedPlan.size}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-1 text-xs text-gray-600 dark:text-gray-400">
                <Clock className="w-3 h-3" />
                <span>{countdown.hours}h: {countdown.minutes}m: {countdown.seconds}s</span>
              </div>
            </div>

            {/* Coupon Code Section - Moved to top */}
            <Card className="mb-4">
              <CardContent className="p-4">
                <h3 className="font-bold text-gray-900 dark:text-white mb-3 text-sm">Available Coupons</h3>
                <div className="space-y-3">
                  {availableCoupons.map((coupon) => (
                    <div
                      key={coupon.code}
                      className={`border-2 rounded-lg p-3 cursor-pointer transition-all duration-300 ${
                        selectedCoupon === coupon.code
                          ? "border-green-500 bg-green-50 dark:bg-green-900/20"
                          : "border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600"
                      }`}
                      onClick={() => {
                        if (selectedCoupon === coupon.code) {
                          removeCoupon()
                        } else {
                          applyCouponCode(coupon.code)
                        }
                      }}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className={`w-8 h-8 rounded-full bg-gradient-to-r ${coupon.color} flex items-center justify-center`}>
                            <Gift className="w-4 h-4 text-white" />
                          </div>
                          <div>
                            <div className="font-semibold text-gray-900 dark:text-white">{coupon.name}</div>
                            <div className="text-xs text-gray-600 dark:text-gray-400">{coupon.description}</div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {selectedCoupon === coupon.code ? (
                            <Check className="w-5 h-5 text-green-600" />
                          ) : (
                            <Button size="sm" variant="outline" className="text-xs">
                              Apply
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                  {isCouponApplied && (
                    <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Check className="w-4 h-4 text-green-600" />
                          <span className="text-sm font-medium text-green-700 dark:text-green-400">
                            Coupon applied! {availableCoupons.find(c => c.code === selectedCoupon)?.name}
                          </span>
                        </div>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={removeCoupon}
                          className="text-red-600 hover:text-red-700 text-xs"
                        >
                          Remove
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Order Summary */}
            <Card className="mb-4">
              <CardContent className="p-4">
                <h3 className="font-bold text-gray-900 dark:text-white mb-3 text-sm">Order Summary</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Challenge Type:</span>
                    <span className="font-medium">{challengeTypeData[selectedChallengeType]?.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Account Size:</span>
                    <span className="font-medium">{selectedPlan.size}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Platform:</span>
                    <span className="font-medium">{selectedPlan.platform}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Original Price:</span>
                    <span className="font-medium line-through text-gray-500">${selectedPlan.price}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Regular Discount:</span>
                    <span className="font-medium text-green-600">-${parseInt(selectedPlan.price) - parseInt(selectedPlan.discountedPrice)}</span>
                  </div>
                  {isCouponApplied && (
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Coupon Discount:</span>
                      <span className="font-medium text-green-600">-${Math.round(parseInt(selectedPlan.price) * (availableCoupons.find(c => c.code === selectedCoupon)?.discount || 0))}</span>
                    </div>
                  )}
                  <div className="flex justify-between font-bold text-lg pt-2 border-t">
                    <span>Total Amount:</span>
                    <span className="text-green-600">${finalPrice || selectedPlan.discountedPrice}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Payment Method Selection */}
            <Card className="mb-4">
              <CardContent className="p-4">
                <h3 className="font-bold text-gray-900 dark:text-white mb-3 text-sm">Select Payment Method</h3>
                <RadioGroup value={selectedPaymentMethod} onValueChange={setSelectedPaymentMethod}>
                  <div className="grid grid-cols-2 gap-2">
                    {cryptoPayments.map((crypto) => (
                      <div key={crypto.id} className="flex items-center space-x-2">
                        <RadioGroupItem value={crypto.id} id={crypto.id} />
                        <Label htmlFor={crypto.id} className="flex-1 cursor-pointer">
                          <div className="flex items-center justify-between p-2 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <div className="flex items-center gap-2">
                              <div className="w-6 h-6 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
                                <Image
                                  src={crypto.logoUrl}
                                  alt={`${crypto.name} logo`}
                                  width={16}
                                  height={16}
                                  className="w-4 h-4 object-contain"
                                />
                              </div>
                              <div>
                                <div className="font-medium text-xs">{crypto.symbol}</div>
                                <div className="text-xs text-gray-600 dark:text-gray-400 truncate">{crypto.name}</div>
                              </div>
                            </div>
                          </div>
                        </Label>
                      </div>
                    ))}
                  </div>
                </RadioGroup>
              </CardContent>
            </Card>

            {/* Payment Address Section */}
            {selectedPaymentMethod && (
              <Card className="mb-4">
                <CardContent className="p-4">
                  <h3 className="font-bold text-gray-900 dark:text-white mb-3 text-sm">Payment Address</h3>
                  <div className="space-y-3">
                    <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-xs font-medium text-gray-600 dark:text-gray-400">Token:</span>
                        <span className="text-sm font-bold text-gray-900 dark:text-white">
                          {paymentInfo[selectedPaymentMethod as keyof typeof paymentInfo]?.token}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-xs font-medium text-gray-600 dark:text-gray-400">Address:</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyAddress(paymentInfo[selectedPaymentMethod as keyof typeof paymentInfo]?.address || "")}
                          className="text-xs p-1 h-auto"
                        >
                          <Copy className="w-3 h-3 mr-1" />
                          Copy
                        </Button>
                      </div>
                      <div className="bg-white dark:bg-gray-700 rounded p-2 mt-2">
                        <p className="text-xs font-mono text-gray-900 dark:text-white break-all">
                          {paymentInfo[selectedPaymentMethod as keyof typeof paymentInfo]?.address}
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* QR Code Section */}
            {selectedPaymentMethod && (
              <Card className="mb-4">
                <CardContent className="p-4">
                  <h3 className="font-bold text-gray-900 dark:text-white mb-3 text-sm">Payment QR Code</h3>
                  <div className="text-center">
                    <div className="w-48 h-48 mx-auto bg-white p-4 rounded-lg border-2 border-gray-200 dark:border-gray-700 mb-3">
                      <div className="w-full h-full bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center">
                        <Image
                          src={paymentInfo[selectedPaymentMethod as keyof typeof paymentInfo]?.qrCode || "/placeholder.jpg"}
                          alt={`${paymentInfo[selectedPaymentMethod as keyof typeof paymentInfo]?.token} QR Code`}
                          width={192}
                          height={192}
                          className="w-full h-full object-contain"
                        />
                      </div>
                    </div>
                    <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">
                      Scan QR code to pay ${finalPrice || selectedPlan.discountedPrice}
                    </p>
                    <Button
                      variant="outline"
                      size="sm"
                      className="text-xs"
                      onClick={() => copyAddress(paymentInfo[selectedPaymentMethod as keyof typeof paymentInfo]?.address || "")}
                    >
                      Copy Address
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Transaction ID Input */}
            <Card className="mb-4">
              <CardContent className="p-4">
                <h3 className="font-bold text-gray-900 dark:text-white mb-3 text-sm">Transaction ID (TXID)</h3>
                <div className="space-y-3">
                  <div>
                    <Label htmlFor="txid" className="text-xs text-gray-600 dark:text-gray-400">
                      Enter the transaction ID from your payment
                    </Label>
                    <Input
                      id="txid"
                      placeholder="e.g., 0x1234567890abcdef..."
                      value={txid}
                      onChange={(e) => setTxid(e.target.value)}
                      className="mt-1"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Image Upload */}
            <Card className="mb-4">
              <CardContent className="p-4">
                <h3 className="font-bold text-gray-900 dark:text-white mb-3 text-sm">Payment Confirmation</h3>
                <div className="space-y-3">
                  <div>
                    <Label htmlFor="payment-image" className="text-xs text-gray-600 dark:text-gray-400">
                      Upload payment confirmation screenshot
                    </Label>
                    <div className="mt-2">
                      <input
                        type="file"
                        id="payment-image"
                        accept="image/*"
                        onChange={handleImageUpload}
                        className="hidden"
                      />
                      <label htmlFor="payment-image" className="cursor-pointer">
                        <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center hover:border-blue-500 dark:hover:border-blue-400 transition-colors">
                          {imagePreview ? (
                            <div className="space-y-2">
                              <img
                                src={imagePreview}
                                alt="Payment confirmation"
                                className="w-full h-32 object-cover rounded-lg"
                              />
                              <p className="text-xs text-green-600">Image uploaded successfully</p>
                            </div>
                          ) : (
                            <div className="space-y-2">
                              <Upload className="w-8 h-8 mx-auto text-gray-400" />
                              <p className="text-sm text-gray-600 dark:text-gray-400">
                                Click to upload payment screenshot
                              </p>
                              <p className="text-xs text-gray-500 dark:text-gray-500">
                                PNG, JPG up to 5MB
                              </p>
                            </div>
                          )}
                        </div>
                      </label>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Terms and Submit Button */}
            <div className="mt-auto space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="terms" 
                  checked={agreedToTerms}
                  onCheckedChange={(checked) => setAgreedToTerms(checked as boolean)}
                />
                <Label htmlFor="terms" className="text-xs">
                  I agree to the{" "}
                  <Link href="/terms" className="text-blue-600 hover:underline">
                    Terms of Service
                  </Link>{" "}
                  &{" "}
                  <Link href="/challenge-terms" className="text-blue-600 hover:underline">
                    Challenge Terms
                  </Link>
                </Label>
              </div>
              
              <Button 
                className="w-full bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white py-3 text-sm font-semibold"
                disabled={!selectedPaymentMethod || !txid.trim() || !uploadedImage || !agreedToTerms || isSubmittingPayment}
                onClick={handleSubmitPayment}
              >
                {isSubmittingPayment ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Submitting Payment...
                  </>
                ) : (
                  <>
                    <Receipt className="w-4 h-4 mr-2" />
                    Submit Payment Confirmation
                  </>
                )}
              </Button>
              
              <div className="text-center text-xs text-gray-600 dark:text-gray-400">
                Amount: ${finalPrice || selectedPlan.discountedPrice}
              </div>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </div>
  )
}