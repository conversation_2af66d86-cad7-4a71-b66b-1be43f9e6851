"use client"

import { <PERSON><PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Wallet,
  DollarSign,
  CreditCard,
  Building2,
  CreditCard as PayPal,
  Bitcoin,
  ArrowRight,
  CheckCircle,
  Clock,
  AlertCircle,
  Shield,
  FileText,
  XCircle,
} from "lucide-react"
import { useState } from "react"
import Link from "next/link"
import { useKYC } from "@/contexts/kyc-context"

export default function Withdraw() {
  const [withdrawMethod, setWithdrawMethod] = useState("bank")
  const [amount, setAmount] = useState("")
  const { kycData, isKYCCompleted, canWithdraw } = useKYC()

  const availableBalance = 0.00
  const pendingWithdrawals = 0.00

  const withdrawMethods = [
    {
      id: "bank",
      name: "Bank Transfer",
      icon: Building2,
      description: "2-5 business days",
      fee: "Free",
    },
    {
      id: "paypal",
      name: "PayPal",
      icon: PayPal,
      description: "Instant",
      fee: "$2.50",
    },
    {
      id: "crypto",
      name: "Cryptocurrency",
      icon: Bitcoin,
      description: "1-2 hours",
      fee: "Free",
    },
  ]

  const recentTransactions: any[] = []

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
      case "pending":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"
      case "failed":
        return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Withdraw Funds</h1>
        <p className="text-gray-600 dark:text-gray-300">Withdraw your profits to your preferred payment method</p>
      </div>

      {/* Balance Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                <Wallet className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 dark:text-white">Available Balance</h3>
                <p className="text-2xl font-bold text-green-600 dark:text-green-400">${availableBalance.toFixed(2)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center">
                <Clock className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 dark:text-white">Pending Withdrawals</h3>
                <p className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">${pendingWithdrawals.toFixed(2)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* KYC Verification Notice */}
      {!isKYCCompleted && (
        <Card className="bg-orange-50 dark:bg-orange-900/20 border-orange-200 dark:border-orange-800">
          <CardContent className="pt-6">
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                <Shield className="w-6 h-6 text-orange-600 dark:text-orange-400" />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2 flex items-center gap-2">
                  <XCircle className="w-5 h-5 text-orange-600 dark:text-orange-400" />
                  KYC Verification Required
                </h3>
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  To withdraw your profits, you must complete our KYC (Know Your Customer) verification process.
                  This includes passing evaluation challenges to ensure compliance with financial regulations.
                </p>
                <div className="flex flex-col sm:flex-row gap-3">
                  <Link href="/dashboard/kyc">
                    <Button className="bg-orange-600 hover:bg-orange-700 text-white">
                      <FileText className="w-4 h-4 mr-2" />
                      Start KYC Verification
                    </Button>
                  </Link>
                  <Button variant="outline" className="border-orange-200 text-orange-700 hover:bg-orange-50 dark:border-orange-800 dark:text-orange-400 dark:hover:bg-orange-900/20">
                    Learn More About KYC
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Withdraw Form */}
      <Card className={!isKYCCompleted ? "opacity-50" : ""}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            New Withdrawal
            {!isKYCCompleted && (
              <Badge className="bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400">
                KYC Required
              </Badge>
            )}
          </CardTitle>
          <CardDescription>
            {isKYCCompleted
              ? "Choose your withdrawal method and amount"
              : "Complete KYC verification to enable withdrawals"
            }
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Withdrawal Method */}
          <div className="space-y-4">
            <Label>Withdrawal Method</Label>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {withdrawMethods.map((method) => {
                const IconComponent = method.icon
                return (
                  <Card
                    key={method.id}
                    className={`transition-all duration-200 ${
                      !isKYCCompleted
                        ? "cursor-not-allowed opacity-50"
                        : "cursor-pointer hover:shadow-lg"
                    } ${
                      withdrawMethod === method.id && isKYCCompleted
                        ? "ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-900/20"
                        : isKYCCompleted ? "hover:bg-gray-50 dark:hover:bg-gray-800" : ""
                    }`}
                    onClick={() => isKYCCompleted && setWithdrawMethod(method.id)}
                  >
                    <CardContent className="pt-6">
                      <div className="flex items-center gap-3 mb-2">
                        <IconComponent className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                        <span className="font-semibold text-gray-900 dark:text-white">{method.name}</span>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">{method.description}</p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">Fee: {method.fee}</p>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </div>

          {/* Amount */}
          <div className="space-y-2">
            <Label htmlFor="amount">Amount (USD)</Label>
            <Input
              id="amount"
              type="number"
              placeholder={isKYCCompleted ? "Enter amount" : "Complete KYC to enable"}
              value={amount}
              onChange={(e) => isKYCCompleted && setAmount(e.target.value)}
              disabled={!isKYCCompleted}
              className="text-lg"
            />
            <p className="text-sm text-gray-600 dark:text-gray-300">
              Available: ${availableBalance.toFixed(2)}
            </p>
          </div>

          {/* Quick Amount Buttons */}
          <div className="flex gap-2">
            {[100, 250, 500, 1000].map((quickAmount) => (
              <Button
                key={quickAmount}
                variant="outline"
                size="sm"
                onClick={() => isKYCCompleted && setAmount(quickAmount.toString())}
                disabled={!isKYCCompleted}
                className="flex-1"
              >
                ${quickAmount}
              </Button>
            ))}
          </div>

          {/* Submit Button */}
          <Button
            className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={!isKYCCompleted}
          >
            <Wallet className="w-4 h-4 mr-2" />
            {isKYCCompleted ? "Process Withdrawal" : "Complete KYC to Withdraw"}
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </CardContent>
      </Card>

      {/* Recent Transactions */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Withdrawals</CardTitle>
          <CardDescription>Your recent withdrawal history</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentTransactions.length > 0 ? (
              recentTransactions.map((transaction) => (
                <div
                  key={transaction.id}
                  className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
                >
                  <div className="flex items-center gap-4">
                    <div className="w-10 h-10 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                      <CreditCard className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                    </div>
                    <div>
                      <p className="font-semibold text-gray-900 dark:text-white">{transaction.method}</p>
                      <p className="text-sm text-gray-600 dark:text-gray-300">{transaction.reference}</p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">{transaction.date}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-gray-900 dark:text-white">${transaction.amount.toFixed(2)}</p>
                    <Badge className={getStatusColor(transaction.status)}>
                      {transaction.status}
                    </Badge>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Wallet className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">No Withdrawals Yet</h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Your withdrawal history will appear here once you make your first withdrawal.
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Information */}
      <Card className="bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <AlertCircle className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5" />
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Important Information</h3>
              <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                <li>• <strong>KYC verification is required</strong> before any withdrawals can be processed</li>
                <li>• You must pass evaluation challenges as part of the KYC process</li>
                <li>• Minimum withdrawal amount: $100</li>
                <li>• Maximum withdrawal amount: $10,000 per day</li>
                <li>• Processing times may vary by payment method</li>
                <li>• All withdrawals are subject to verification and compliance checks</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
