"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Gift, Trophy, Star, Target, TrendingUp, Clock } from "lucide-react"
import { useLanguage } from "@/contexts/language-context"

export default function MyRewards() {
  const { t } = useLanguage()

  const rewardStats = [
    {
      label: t("rewards.totalEarned"),
      value: "$2,450",
      change: "+15%",
      color: "text-green-600 dark:text-green-400",
    },
    {
      label: t("rewards.thisMonth"),
      value: "$380",
      change: "+8%",
      color: "text-blue-600 dark:text-blue-400",
    },
    {
      label: t("rewards.available"),
      value: "$150",
      change: "Ready",
      color: "text-purple-600 dark:text-purple-400",
    },
    {
      label: t("rewards.pending"),
      value: "$75",
      change: "Processing",
      color: "text-orange-600 dark:text-orange-400",
    },
  ]

  const recentRewards = [
    {
      id: 1,
      type: t("rewards.referralBonus"),
      amount: 50,
      date: "2024-01-20",
      status: "claimed",
      icon: Gift,
    },
    {
      id: 2,
      type: t("rewards.tradingBonus"),
      amount: 100,
      date: "2024-01-18",
      status: "available",
      icon: TrendingUp,
    },
    {
      id: 3,
      type: t("rewards.loyaltyReward"),
      amount: 25,
      date: "2024-01-15",
      status: "claimed",
      icon: Star,
    },
    {
      id: 4,
      type: t("rewards.performanceBonus"),
      amount: 200,
      date: "2024-01-12",
      status: "pending",
      icon: Trophy,
    },
  ]

  const achievements = [
    {
      id: 1,
      title: t("rewards.firstTrade"),
      description: "Complete your first successful trade",
      progress: 100,
      requirement: "1 trade",
      status: "claimed",
      reward: 25,
      earnedDate: "2024-01-10",
    },
    {
      id: 2,
      title: t("rewards.profitTarget"),
      description: "Achieve 5% profit in a single month",
      progress: 80,
      requirement: "5% monthly profit",
      status: "in-progress",
      reward: 100,
      earnedDate: null,
    },
    {
      id: 3,
      title: t("rewards.consistency"),
      description: "Maintain positive returns for 3 consecutive months",
      progress: 33,
      requirement: "3 months positive",
      status: "in-progress",
      reward: 250,
      earnedDate: null,
    },
    {
      id: 4,
      title: t("rewards.riskManagement"),
      description: "Trade for 30 days without violating risk rules",
      progress: 0,
      requirement: "30 days compliant",
      status: "locked",
      reward: 150,
      earnedDate: null,
    },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "claimed":
        return "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400"
      case "available":
        return "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400"
      case "pending":
        return "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400"
      case "in-progress":
        return "bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-400"
      case "locked":
        return "bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-400"
      default:
        return "bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-400"
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case "claimed":
        return t("rewards.claimed")
      case "available":
        return t("rewards.available")
      case "pending":
        return t("rewards.pending")
      case "in-progress":
        return t("rewards.progress")
      case "locked":
        return t("rewards.locked")
      default:
        return status
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">{t("rewards.title")}</h1>
        <p className="text-gray-600 dark:text-gray-400">{t("rewards.subtitle")}</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {rewardStats.map((stat, index) => (
          <div
            key={index}
            className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">{stat.label}</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{stat.value}</p>
              </div>
              <div className={`text-sm font-medium ${stat.color}`}>{stat.change}</div>
            </div>
          </div>
        ))}
      </div>

      {/* Recent Rewards */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">{t("rewards.recent")}</h2>
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {recentRewards.map((reward) => {
              const IconComponent = reward.icon
              return (
                <div key={reward.id} className="p-6 flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center">
                      <IconComponent className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{reward.type}</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {t("rewards.earnedOn")}: {reward.date}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="text-right">
                      <p className="text-lg font-bold text-green-600 dark:text-green-400">+${reward.amount}</p>
                      <Badge className={getStatusColor(reward.status)}>{getStatusText(reward.status)}</Badge>
                    </div>
                    {reward.status === "available" && (
                      <Button size="sm" className="bg-green-600 hover:bg-green-700">
                        {t("rewards.claim")}
                      </Button>
                    )}
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </div>

      {/* Achievements */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">{t("rewards.achievements")}</h2>
        <div className="grid gap-6">
          {achievements.map((achievement) => (
            <div
              key={achievement.id}
              className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
                    <Target className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{achievement.title}</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{achievement.description}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div className="text-right">
                    <p className="text-lg font-bold text-purple-600 dark:text-purple-400">+${achievement.reward}</p>
                    <Badge className={getStatusColor(achievement.status)}>{getStatusText(achievement.status)}</Badge>
                  </div>
                </div>
              </div>

              {achievement.status === "in-progress" && (
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {t("rewards.progress")}
                    </span>
                    <span className="text-sm text-gray-600 dark:text-gray-400">{achievement.progress}%</span>
                  </div>
                  <Progress value={achievement.progress} className="h-2" />
                </div>
              )}

              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                  <Clock className="w-4 h-4" />
                  <span>
                    {t("rewards.requirement")}: {achievement.requirement}
                  </span>
                </div>
                {achievement.earnedDate && (
                  <span className="text-gray-600 dark:text-gray-400">
                    {t("rewards.earnedOn")}: {achievement.earnedDate}
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
