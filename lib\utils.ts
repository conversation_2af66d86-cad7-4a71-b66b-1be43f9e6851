import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Toast utility functions for consistent styling
export const toastStyles = {
  success: {
    background: 'linear-gradient(135deg, #10b981, #059669)',
    color: 'white',
    border: '1px solid #10b981',
    borderRadius: '12px',
    boxShadow: '0 10px 25px rgba(16, 185, 129, 0.2)',
  },
  error: {
    background: 'linear-gradient(135deg, #ef4444, #dc2626)',
    color: 'white',
    border: '1px solid #ef4444',
    borderRadius: '12px',
    boxShadow: '0 10px 25px rgba(239, 68, 68, 0.2)',
  },
  warning: {
    background: 'linear-gradient(135deg, #f59e0b, #d97706)',
    color: 'white',
    border: '1px solid #f59e0b',
    borderRadius: '12px',
    boxShadow: '0 10px 25px rgba(245, 158, 11, 0.2)',
  },
  info: {
    background: 'linear-gradient(135deg, #3b82f6, #2563eb)',
    color: 'white',
    border: '1px solid #3b82f6',
    borderRadius: '12px',
    boxShadow: '0 10px 25px rgba(59, 130, 246, 0.2)',
  },
}
