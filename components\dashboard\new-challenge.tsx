"use client"

import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  TrendingUp,
  DollarSign,
  Target,
  Calendar,
  Award,
  Shield,
  Zap,
  ArrowRight,
  CheckCircle,
  Star,
  Clock,
  Users,
  Crown,
  Rocket,
  Gift,
  CreditCard,
  Wallet,
  Loader2,
  Copy,
} from "lucide-react"
import { useState } from "react"
import Image from "next/image"
import { useAuth } from "@/contexts/auth-context"
import { toast } from "sonner"
import { toastStyles } from "@/lib/utils"

export default function NewChallenge() {
  const [selectedAccount, setSelectedAccount] = useState("50k")
  const [selectedPlatform, setSelectedPlatform] = useState("mt5")
  const [selectedPayment, setSelectedPayment] = useState("bitcoin")
  const [selectedChallengeType, setSelectedChallengeType] = useState("1-phase")
  const [isPlacingOrder, setIsPlacingOrder] = useState(false)
  const { user, isAuthenticated, logout } = useAuth()

  const accountTypes = [
    {
      id: "25k",
      name: "$25,000",
      price: "$155",
      originalPrice: "$299",
      description: "Perfect for beginners",
      features: ["8% profit target", "5% max drawdown", "30 days time limit", "80% profit split"],
      popular: false,
      savings: "48% OFF",
    },
    {
      id: "50k",
      name: "$50,000",
      price: "$250",
      originalPrice: "$499",
      description: "Most popular choice",
      features: ["8% profit target", "5% max drawdown", "30 days time limit", "80% profit split"],
      popular: true,
      savings: "50% OFF",
    },
    {
      id: "100k",
      name: "$100,000",
      price: "$345",
      originalPrice: "$699",
      description: "For experienced traders",
      features: ["8% profit target", "5% max drawdown", "30 days time limit", "80% profit split"],
      popular: false,
      savings: "51% OFF",
    },
    {
      id: "200k",
      name: "$200,000",
      price: "$495",
      originalPrice: "$999",
      description: "Maximum account size",
      features: ["8% profit target", "5% max drawdown", "30 days time limit", "80% profit split"],
      popular: false,
      savings: "50% OFF",
    },
  ]

  const features = [
    {
      icon: Target,
      title: "8% Profit Target",
      description: "Reach 8% profit to pass Phase 1",
      color: "text-green-600",
      bgColor: "bg-green-100 dark:bg-green-900/30",
    },
    {
      icon: Shield,
      title: "5% Max Drawdown",
      description: "Stay within 5% daily drawdown limit",
      color: "text-orange-600",
      bgColor: "bg-orange-100 dark:bg-orange-900/30",
    },
    {
      icon: Calendar,
      title: "30 Days Time Limit",
      description: "Complete Phase 1 within 30 days",
      color: "text-blue-600",
      bgColor: "bg-blue-100 dark:bg-blue-900/30",
    },
    {
      icon: Award,
      title: "80% Profit Split",
      description: "Keep 80% of your profits",
      color: "text-purple-600",
      bgColor: "bg-purple-100 dark:bg-purple-900/30",
    },
  ]

  const platforms = [
    {
      id: "mt5",
      name: "MetaTrader 5",
      description: "Advanced trading platform",
      logoUrl: "https://res.cloudinary.com/dufcjjaav/image/upload/v1752130069/512x512bb-removebg-preview_qnfrll.png",
      features: ["Advanced charting", "Multiple timeframes", "Expert Advisors", "Mobile trading"],
      popular: true,
    },
    {
      id: "mt4",
      name: "MetaTrader 4",
      description: "Classic trading platform",
      logoUrl: "https://res.cloudinary.com/dufcjjaav/image/upload/v1752130069/512x512bb-removebg-preview_qnfrll.png",
      features: ["User-friendly interface", "Custom indicators", "Automated trading", "Market analysis"],
      popular: false,
    },
  ]

  const challengeTypes = [
    {
      id: "1-phase",
      name: "1 Phase",
      description: "Single phase challenge",
      active: selectedChallengeType === "1-phase"
    },
    {
      id: "2-phase",
      name: "2 Phase",
      description: "Two phase challenge",
      active: selectedChallengeType === "2-phase"
    },
    { 
      id: "hft", 
      name: "HFT", 
      description: "High frequency trading",
      active: selectedChallengeType === "hft" 
    },
    { 
      id: "instant", 
      name: "Instant", 
      description: "Instant funding",
      active: selectedChallengeType === "instant" 
    }
  ]

  const paymentMethods = [
    {
      id: "bitcoin",
      name: "Bitcoin",
      symbol: "BTC",
      logoUrl: "https://cryptologos.cc/logos/bitcoin-btc-logo.png",
      qrCode: "/BTC.jpg",
      color: "from-orange-400 to-orange-600",
      address: "******************************************",
    },
    {
      id: "ethereum",
      name: "Ethereum",
      symbol: "ETH",
      logoUrl: "https://cryptologos.cc/logos/ethereum-eth-logo.png",
      qrCode: "/Eth.jpg",
      color: "from-blue-400 to-blue-600",
      address: "******************************************",
    },
    {
      id: "usdt_trc20",
      name: "USDT (TRC20)",
      symbol: "USDT",
      logoUrl: "https://cryptologos.cc/logos/tether-usdt-logo.png",
      qrCode: "/Usdt trc20.jpg",
      color: "from-green-400 to-green-600",
      address: "TQn9Y2khDD95J42FQtQTdwVVRZ1qJqgJq",
    },
    {
      id: "usdt_bep20",
      name: "USDT (BEP20)",
      symbol: "USDT",
      logoUrl: "https://cryptologos.cc/logos/tether-usdt-logo.png",
      qrCode: "/Usdt bep20.jpg",
      color: "from-green-400 to-green-600",
      address: "******************************************",
    },
    {
      id: "usdt_polygon",
      name: "USDT (Polygon)",
      symbol: "USDT",
      logoUrl: "https://cryptologos.cc/logos/tether-usdt-logo.png",
      qrCode: "/Usdt polygon.jpg",
      color: "from-green-400 to-green-600",
      address: "******************************************",
    },
    {
      id: "solana",
      name: "Solana",
      symbol: "SOL",
      logoUrl: "https://cryptologos.cc/logos/solana-sol-logo.png",
      qrCode: "/Solana.jpg",
      color: "from-purple-400 to-purple-600",
      address: "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM",
    },
  ]

  const handlePlaceOrder = async () => {
    if (!isAuthenticated) {
      toast.error("Authentication required", {
        description: "Please login to place an order.",
        duration: 4000,
      })
      return
    }

    setIsPlacingOrder(true)
    
    try {
      const token = localStorage.getItem('auth_token')
      
      if (!token) {
        toast.error("Authentication token not found", {
          description: "Please login again to continue.",
          duration: 4000,
        })
        return
      }

      // Check if this is a mock token (development fallback)
      const isMockToken = token.startsWith('mock_jwt_token_')

      if (isMockToken) {
        console.log('Using mock token - simulating order placement for development')
        // Simulate successful order placement for development
        await new Promise(resolve => setTimeout(resolve, 2000)) // Simulate API delay
        
        toast.success("Order placed successfully! (Development mode)", {
          description: "This is a development simulation using mock authentication.",
          duration: 4000,
          style: toastStyles.success,
          icon: "🔧",
        })
        return
      }

      // Get the selected account size from the accountTypes array
      const selectedAccountData = accountTypes.find(acc => acc.id === selectedAccount)
      const accountSize = selectedAccountData?.name.replace(/[$,]/g, '') || selectedAccount

      // Get the selected platform name
      const selectedPlatformData = platforms.find(platform => platform.id === selectedPlatform)
      const platform = selectedPlatformData?.name || selectedPlatform

      // Get the selected payment method name
      const selectedPaymentData = paymentMethods.find(method => method.id === selectedPayment)
      const paymentMethod = selectedPaymentData?.name || selectedPayment

      // Get the selected challenge type name
      const selectedChallengeTypeData = challengeTypes.find(type => type.id === selectedChallengeType)
      const challengeType = selectedChallengeTypeData?.name || selectedChallengeType

      // Create FormData for the order request
      const formData = new FormData()
      formData.append('payment_method', paymentMethod)
      formData.append('platform', platform)
      formData.append('account_size', accountSize)
      formData.append('challenge_type', challengeType)

      console.log('Placing order with FormData:', {
        payment_method: paymentMethod,
        platform: platform,
        account_size: accountSize,
        challenge_type: challengeType
      })

      // Use the real token with Bearer format (same as account details)
      const authHeader = `Bearer ${token}`
      console.log('=== ORDER API DEBUG ===')
      console.log('Token found:', token ? 'Yes' : 'No')
      console.log('Token preview:', token?.substring(0, 30) + '...')
      console.log('Token type:', token?.startsWith('mock_jwt_token_') ? 'MOCK' : 'REAL')
      console.log('Auth header:', authHeader)
      console.log('Using real auth token for order API call')

      const response = await fetch('https://xthronebackend-9dbecc0e3719.herokuapp.com/order/order', {
        method: 'POST',
        headers: {
          'Authorization': authHeader,
          // Remove Content-Type header - let browser set it for FormData
        },
        body: formData,  // Send as FormData, not JSON
      })

      console.log('Order API Response Status:', response.status)
      console.log('Order API Response OK:', response.ok)

      if (!response.ok) {
        console.error('API Response Status:', response.status)
        const responseText = await response.text()
        console.error('API Response Text:', responseText)

        // If unauthorized, the token might be expired or invalid
        if (response.status === 401) {
          console.error('Authentication failed - token may be expired or invalid')

          // Handle authentication failure (same as auth context)
          logout()
          return
        }

        // If validation error, provide specific message
        if (response.status === 422) {
          console.error('Validation error - check if order data format is correct')
          toast.error("Invalid order data", {
            description: "Please check your selections and try again.",
            duration: 5000,
          })
          return
        }

        throw new Error(`Failed to place order: ${response.status} - ${responseText}`)
      }

      const result = await response.json()
      console.log('Order placed successfully:', result)
      
      // Show professional success toast with green styling
      toast.success("Order placed successfully! 🚀", {
        description: "Your trading challenge has been initiated. Check your dashboard for updates and next steps.",
        duration: 6000,
        style: toastStyles.success,
        icon: "🎯",
      })
      
      // Optionally redirect to dashboard or order confirmation page
      // window.location.href = '/dashboard/account-details'
      
    } catch (error) {
      console.error('Error placing order:', error)
      toast.error("Order placement failed", {
        description: error instanceof Error ? error.message : "Please try again later.",
        duration: 5000,
      })
    } finally {
      setIsPlacingOrder(false)
    }
  }

  const handleAccountSelection = (accountId: string) => {
    setSelectedAccount(accountId)
    const account = accountTypes.find(acc => acc.id === accountId)
    if (account) {
      toast.success(`Selected ${account.name} account`, {
        description: `You've chosen the ${account.name} challenge package.`,
        duration: 3000,
        style: toastStyles.success,
        icon: "💼",
      })
    }
  }

  const handlePlatformSelection = (platformId: string) => {
    setSelectedPlatform(platformId)
    const platform = platforms.find(p => p.id === platformId)
    if (platform) {
      toast.success(`Selected ${platform.name}`, {
        description: `You've chosen ${platform.name} as your trading platform.`,
        duration: 3000,
        style: toastStyles.success,
        icon: "📊",
      })
    }
  }

  const handlePaymentSelection = (paymentId: string) => {
    setSelectedPayment(paymentId)
    const payment = paymentMethods.find(p => p.id === paymentId)
    if (payment) {
      toast.success(`Selected ${payment.name}`, {
        description: `You've chosen ${payment.name} as your payment method.`,
        duration: 3000,
        style: toastStyles.success,
        icon: "💳",
      })
    }
  }

  const handleChallengeTypeSelection = (typeId: string) => {
    setSelectedChallengeType(typeId)
    const challengeType = challengeTypes.find(t => t.id === typeId)
    if (challengeType) {
      toast.success(`Selected ${challengeType.name} challenge`, {
        description: `You've chosen the ${challengeType.name} challenge type.`,
        duration: 3000,
        style: toastStyles.success,
        icon: "🎯",
      })
    }
  }

  return (
    <div className="space-y-12">
      {/* Header Section */}
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">Choose Your Challenge</h1>
        <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
          Select the perfect account size for your trading journey. All challenges include our comprehensive evaluation process.
        </p>
      </div>

      {/* Sale Banner */}
      <div className="bg-gradient-to-r from-red-500 to-pink-600 text-white p-6 rounded-xl text-center">
        <div className="flex items-center justify-center gap-4 mb-2">
          <Gift className="w-6 h-6" />
          <h2 className="text-2xl font-bold">SPECIAL OFFER - UP TO 51% OFF!</h2>
          <Gift className="w-6 h-6" />
        </div>
        <p className="text-lg">Use coupon code: <span className="font-bold text-yellow-300 text-xl">SAVE70</span></p>
        <p className="text-sm text-red-100 mt-2">Limited time offer - Don't miss out on massive savings!</p>
      </div>

      {/* Account Selection */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {accountTypes.map((account) => (
          <Card
            key={account.id}
            className={`cursor-pointer transition-all duration-300 hover:shadow-xl hover:scale-105 ${
              selectedAccount === account.id
                ? "ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-900/20 shadow-lg"
                : "hover:bg-gray-50 dark:hover:bg-gray-800"
            } relative overflow-hidden`}
            onClick={() => handleAccountSelection(account.id)}
          >
            {account.popular && (
              <div className="absolute top-0 right-0 bg-blue-600 text-white text-xs font-bold px-3 py-1 rounded-bl-lg">
                MOST POPULAR
              </div>
            )}
            <div className="absolute top-2 left-2">
              <Badge className="bg-red-500 text-white text-xs">{account.savings}</Badge>
            </div>
            
            <CardHeader className="pb-3 pt-8">
              <div className="text-center">
                <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white mb-2">{account.name}</CardTitle>
                <CardDescription className="text-sm">{account.description}</CardDescription>
              </div>
            </CardHeader>
            
            <CardContent className="text-center">
              <div className="mb-4">
                <div className="text-3xl font-bold text-gray-900 dark:text-white">{account.price}</div>
                <div className="text-sm text-gray-500 line-through">{account.originalPrice}</div>
              </div>
              
              <ul className="space-y-3 text-left">
                {account.features.map((feature, index) => (
                  <li key={index} className="flex items-center gap-3 text-sm text-gray-600 dark:text-gray-300">
                    <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
              
              <Button 
                className={`w-full mt-6 ${
                  selectedAccount === account.id
                    ? "bg-blue-600 hover:bg-blue-700 text-white"
                    : "bg-gray-100 hover:bg-gray-200 text-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700 dark:text-gray-300"
                }`}
              >
                {selectedAccount === account.id ? "Selected" : "Choose Plan"}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Challenge Type Selection */}
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Choose Challenge Type</h2>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            Select the type of challenge that suits your trading style
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 max-w-4xl mx-auto">
          {challengeTypes.map((type) => (
            <Card
              key={type.id}
              className={`cursor-pointer transition-all duration-300 hover:shadow-xl hover:scale-105 ${
                selectedChallengeType === type.id
                  ? "ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-900/20 shadow-lg"
                  : "hover:bg-gray-50 dark:hover:bg-gray-800"
              }`}
              onClick={() => handleChallengeTypeSelection(type.id)}
            >
              <CardContent className="pt-6 pb-4">
                <div className="text-center">
                  <CardTitle className="text-lg mb-2">{type.name}</CardTitle>
                  <CardDescription className="text-sm">{type.description}</CardDescription>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Platform Selection */}
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Choose Your Trading Platform</h2>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            Select your preferred trading platform for the challenge
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
          {platforms.map((platform) => (
            <Card
              key={platform.id}
              className={`cursor-pointer transition-all duration-300 hover:shadow-xl hover:scale-105 ${
                selectedPlatform === platform.id
                  ? "ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-900/20 shadow-lg"
                  : "hover:bg-gray-50 dark:hover:bg-gray-800"
              } relative overflow-hidden`}
              onClick={() => handlePlatformSelection(platform.id)}
            >
              {platform.popular && (
                <div className="absolute top-0 right-0 bg-green-600 text-white text-xs font-bold px-3 py-1 rounded-bl-lg">
                  RECOMMENDED
                </div>
              )}

              <CardHeader className="pb-3">
                <div className="flex items-center gap-4">
                  <div className="w-16 h-16 flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-xl">
                    <Image
                      src={platform.logoUrl}
                      alt={`${platform.name} logo`}
                      width={48}
                      height={48}
                      className="w-12 h-12 object-contain"
                    />
                  </div>
                  <div>
                    <CardTitle className="text-xl font-bold text-gray-900 dark:text-white">{platform.name}</CardTitle>
                    <CardDescription>{platform.description}</CardDescription>
                  </div>
                </div>
              </CardHeader>

              <CardContent>
                <ul className="space-y-2">
                  {platform.features.map((feature, index) => (
                    <li key={index} className="flex items-center gap-3 text-sm text-gray-600 dark:text-gray-300">
                      <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>

                <Button
                  className={`w-full mt-4 ${
                    selectedPlatform === platform.id
                      ? "bg-blue-600 hover:bg-blue-700 text-white"
                      : "bg-gray-100 hover:bg-gray-200 text-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700 dark:text-gray-300"
                  }`}
                >
                  {selectedPlatform === platform.id ? "Selected" : "Choose Platform"}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Payment Method Selection */}
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Payment Method</h2>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            Choose your preferred cryptocurrency for payment
          </p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-4xl mx-auto">
          {paymentMethods.map((method) => (
            <Card
              key={method.id}
              className={`cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-105 ${
                selectedPayment === method.id
                  ? "ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-900/20 shadow-lg"
                  : "hover:bg-gray-50 dark:hover:bg-gray-800"
              }`}
              onClick={() => handlePaymentSelection(method.id)}
            >
              <CardContent className="pt-6 pb-4">
                <div className="text-center">
                  <div className={`w-16 h-16 mx-auto mb-3 rounded-full bg-gradient-to-r ${method.color} flex items-center justify-center p-3`}>
                    <Image
                      src={method.logoUrl}
                      alt={`${method.name} logo`}
                      width={40}
                      height={40}
                      className="w-10 h-10 object-contain"
                    />
                  </div>
                  <h3 className="font-semibold text-gray-900 dark:text-white text-sm">{method.name}</h3>
                  <p className="text-xs text-gray-500 dark:text-gray-400">{method.symbol}</p>
                  {selectedPayment === method.id && (
                    <CheckCircle className="w-5 h-5 text-green-500 mx-auto mt-2" />
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* QR Code Display Section */}
      {selectedPayment && (
        <div className="space-y-6">
          <div className="text-center">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Payment Details</h3>
            <p className="text-lg text-gray-600 dark:text-gray-300">
              Scan the QR code or copy the address to complete your payment
            </p>
          </div>

          <div className="max-w-2xl mx-auto">
            <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-2 border-blue-200 dark:border-blue-400/20">
              <CardContent className="p-8">
                <div className="text-center space-y-6">
                  {/* Selected Payment Method Info */}
                  <div className="flex items-center justify-center gap-4 mb-6">
                    {(() => {
                      const selectedMethod = paymentMethods.find(method => method.id === selectedPayment)
                      return (
                        <>
                          <div className={`w-16 h-16 rounded-full bg-gradient-to-r ${selectedMethod?.color} flex items-center justify-center p-3`}>
                            <Image
                              src={selectedMethod?.logoUrl || ""}
                              alt={`${selectedMethod?.name} logo`}
                              width={40}
                              height={40}
                              className="w-10 h-10 object-contain"
                            />
                          </div>
                          <div className="text-left">
                            <h4 className="text-xl font-bold text-gray-900 dark:text-white">{selectedMethod?.name}</h4>
                            <p className="text-gray-600 dark:text-gray-300">{selectedMethod?.symbol}</p>
                          </div>
                        </>
                      )
                    })()}
                  </div>

                  {/* QR Code */}
                  <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg inline-block">
                    <div className="w-48 h-48 mx-auto relative">
                      <Image
                        src={paymentMethods.find(method => method.id === selectedPayment)?.qrCode || ""}
                        alt="Payment QR Code"
                        fill
                        className="object-contain rounded-lg"
                      />
                    </div>
                  </div>

                  {/* Wallet Address */}
                  <div className="space-y-3">
                    <p className="text-sm text-gray-600 dark:text-gray-400">Wallet Address:</p>
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                      <div className="flex items-center justify-between gap-4">
                        <code className="text-sm text-gray-900 dark:text-white break-all">
                          {paymentMethods.find(method => method.id === selectedPayment)?.address}
                        </code>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            navigator.clipboard.writeText(paymentMethods.find(method => method.id === selectedPayment)?.address || "")
                            toast.success("Address copied to clipboard!", {
                              description: "Wallet address has been copied to your clipboard.",
                              duration: 3000,
                            })
                          }}
                          className="flex-shrink-0"
                        >
                          <Copy className="w-4 h-4 mr-2" />
                          Copy
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* Payment Instructions */}
                  <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-400/20">
                    <h5 className="font-semibold text-blue-900 dark:text-blue-300 mb-2">Payment Instructions:</h5>
                    <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                      <li>• Send the exact amount to the address above</li>
                      <li>• Use the correct network for your selected cryptocurrency</li>
                      <li>• Keep your transaction ID for verification</li>
                      <li>• Payment will be confirmed within 10-30 minutes</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Features Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {features.map((feature, index) => {
          const IconComponent = feature.icon
          return (
            <Card key={index} className="text-center hover:shadow-lg transition-all duration-200">
              <CardContent className="pt-6">
                <div className={`w-16 h-16 mx-auto mb-4 rounded-full ${feature.bgColor} flex items-center justify-center`}>
                  <IconComponent className={`w-8 h-8 ${feature.color}`} />
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2 text-lg">{feature.title}</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">{feature.description}</p>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Action Section */}
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
        <CardContent className="pt-8 pb-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Ready to Start Your FxThrone Challenge?
            </h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-3xl mx-auto text-lg">
              Join thousands of successful traders who have passed the FxThrone Challenge and are now trading with funded accounts.
              Start your journey to financial freedom today!
            </p>

            {/* Selection Summary */}
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 mb-8 max-w-2xl mx-auto">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Your Selection Summary</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                <div className="flex items-center gap-3">
                  <DollarSign className="w-5 h-5 text-green-600" />
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">Account Size</p>
                    <p className="text-gray-600 dark:text-gray-300">
                      {accountTypes.find(acc => acc.id === selectedAccount)?.name}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Target className="w-5 h-5 text-orange-600" />
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">Challenge Type</p>
                    <p className="text-gray-600 dark:text-gray-300">
                      {challengeTypes.find(type => type.id === selectedChallengeType)?.name}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <TrendingUp className="w-5 h-5 text-blue-600" />
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">Platform</p>
                    <p className="text-gray-600 dark:text-gray-300">
                      {platforms.find(platform => platform.id === selectedPlatform)?.name}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Wallet className="w-5 h-5 text-purple-600" />
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">Payment</p>
                    <p className="text-gray-600 dark:text-gray-300">
                      {paymentMethods.find(method => method.id === selectedPayment)?.name}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg"
                onClick={handlePlaceOrder}
                disabled={isPlacingOrder}
              >
                {isPlacingOrder ? (
                  <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                ) : (
                  <Rocket className="w-5 h-5 mr-2" />
                )}
                {isPlacingOrder ? "Placing Order..." : "Start Challenge Now"}
                {!isPlacingOrder && <ArrowRight className="w-5 h-5 ml-2" />}
              </Button>
              <Button variant="outline" className="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 px-8 py-4 text-lg">
                <Crown className="w-5 h-5 mr-2" />
                Learn More
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="hover:shadow-lg transition-all duration-200">
          <CardContent className="pt-6 text-center">
            <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
              <Users className="w-8 h-8 text-green-600 dark:text-green-400" />
            </div>
            <div className="text-3xl font-bold text-gray-900 dark:text-white mb-1">50,000+</div>
            <div className="text-sm text-gray-600 dark:text-gray-300">Active Traders</div>
          </CardContent>
        </Card>
        <Card className="hover:shadow-lg transition-all duration-200">
          <CardContent className="pt-6 text-center">
            <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
              <Award className="w-8 h-8 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="text-3xl font-bold text-gray-900 dark:text-white mb-1">$500M+</div>
            <div className="text-sm text-gray-600 dark:text-gray-300">Total Payouts</div>
          </CardContent>
        </Card>
        <Card className="hover:shadow-lg transition-all duration-200">
          <CardContent className="pt-6 text-center">
            <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center">
              <Star className="w-8 h-8 text-purple-600 dark:text-purple-400" />
            </div>
            <div className="text-3xl font-bold text-gray-900 dark:text-white mb-1">4.8/5</div>
            <div className="text-sm text-gray-600 dark:text-gray-300">Trustpilot Rating</div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
