"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calculator } from "lucide-react"
import { useLanguage } from "@/contexts/language-context"

type CalculatorTab = 'margin' | 'profit-loss' | 'lot-size'

interface MarginCalculatorState {
  accountModel: string
  accountBalance: string
  instrument: string
  currentPrice: string
  lotSize: string
}

interface ProfitLossCalculatorState {
  accountModel: string
  accountBalance: string
  instrument: string
  openPrice: string
  closePrice: string
  lotSize: string
  tradeType: string
}

interface LotSizeCalculatorState {
  accountModel: string
  accountBalance: string
  instrument: string
  riskPercentage: string
  stopLoss: string
  currentPrice: string
}

export default function ProfitCalculator() {
  const { t } = useLanguage()
  const [activeTab, setActiveTab] = useState<CalculatorTab>('margin')
  const [result, setResult] = useState<string>('$0.00')

  const [marginState, setMarginState] = useState<MarginCalculatorState>({
    accountModel: '',
    accountBalance: '',
    instrument: '',
    currentPrice: '',
    lotSize: ''
  })

  const [profitLossState, setProfitLossState] = useState<ProfitLossCalculatorState>({
    accountModel: '',
    accountBalance: '',
    instrument: '',
    openPrice: '',
    closePrice: '',
    lotSize: '',
    tradeType: 'buy'
  })

  const [lotSizeState, setLotSizeState] = useState<LotSizeCalculatorState>({
    accountModel: '',
    accountBalance: '',
    instrument: '',
    riskPercentage: '',
    stopLoss: '',
    currentPrice: ''
  })

  // Account models data
  const accountModels = [
    { value: 'stellar-challenge', label: 'Stellar Challenge' },
    { value: 'stellar-instant', label: 'Stellar Instant' },
    { value: 'legacy-challenge', label: 'Legacy Challenge' },
    { value: 'rapid-challenge', label: 'Rapid Challenge' }
  ]

  // Account balances data
  const accountBalances = [
    { value: '5000', label: '$5,000' },
    { value: '10000', label: '$10,000' },
    { value: '25000', label: '$25,000' },
    { value: '50000', label: '$50,000' },
    { value: '100000', label: '$100,000' },
    { value: '200000', label: '$200,000' }
  ]

  // Trading instruments data
  const instruments = [
    { value: 'EURUSD', label: 'EUR/USD' },
    { value: 'GBPUSD', label: 'GBP/USD' },
    { value: 'USDJPY', label: 'USD/JPY' },
    { value: 'AUDUSD', label: 'AUD/USD' },
    { value: 'USDCAD', label: 'USD/CAD' },
    { value: 'XAUUSD', label: 'XAU/USD (Gold)' },
    { value: 'US30', label: 'US30' },
    { value: 'SPX500', label: 'SPX500' }
  ]

  // Calculation functions
  const calculateMargin = () => {
    if (!marginState.currentPrice || !marginState.lotSize || !marginState.instrument) {
      setResult('$0.00')
      return
    }

    try {
      const price = parseFloat(marginState.currentPrice)
      const lots = parseFloat(marginState.lotSize)

      if (price <= 0 || lots <= 0) {
        setResult('$0.00')
        return
      }

      // Different leverage based on instrument type
      let leverage = 100
      if (marginState.instrument.includes('XAU') || marginState.instrument.includes('Gold')) {
        leverage = 50 // Gold typically has lower leverage
      } else if (marginState.instrument.includes('US30') || marginState.instrument.includes('SPX')) {
        leverage = 20 // Indices typically have lower leverage
      }

      // Contract size varies by instrument
      let contractSize = 100000 // Standard for forex pairs
      if (marginState.instrument.includes('JPY')) {
        contractSize = 100000
      } else if (marginState.instrument.includes('XAU')) {
        contractSize = 100 // Gold
      } else if (marginState.instrument.includes('US30')) {
        contractSize = 5 // US30
      } else if (marginState.instrument.includes('SPX')) {
        contractSize = 50 // SPX500
      }

      const margin = (price * lots * contractSize) / leverage
      setResult(`$${margin.toFixed(2)}`)
    } catch (error) {
      setResult('$0.00')
    }
  }

  const calculateProfitLoss = () => {
    if (!profitLossState.openPrice || !profitLossState.closePrice || !profitLossState.lotSize || !profitLossState.instrument) {
      setResult('$0.00')
      return
    }

    try {
      const openPrice = parseFloat(profitLossState.openPrice)
      const closePrice = parseFloat(profitLossState.closePrice)
      const lots = parseFloat(profitLossState.lotSize)

      if (openPrice <= 0 || closePrice <= 0 || lots <= 0) {
        setResult('$0.00')
        return
      }

      // Contract size varies by instrument
      let contractSize = 100000 // Standard for forex pairs
      if (profitLossState.instrument.includes('JPY')) {
        contractSize = 100000
      } else if (profitLossState.instrument.includes('XAU')) {
        contractSize = 100 // Gold
      } else if (profitLossState.instrument.includes('US30')) {
        contractSize = 5 // US30
      } else if (profitLossState.instrument.includes('SPX')) {
        contractSize = 50 // SPX500
      }

      let priceDiff = profitLossState.tradeType === 'buy'
        ? closePrice - openPrice
        : openPrice - closePrice

      const profitLoss = priceDiff * lots * contractSize

      const sign = profitLoss >= 0 ? '+' : ''
      setResult(`${sign}$${profitLoss.toFixed(2)}`)
    } catch (error) {
      setResult('$0.00')
    }
  }

  const calculateLotSize = () => {
    if (!lotSizeState.accountBalance || !lotSizeState.riskPercentage || !lotSizeState.stopLoss) {
      setResult('0.00')
      return
    }

    try {
      const balance = parseFloat(lotSizeState.accountBalance)
      const riskPercent = parseFloat(lotSizeState.riskPercentage) / 100
      const stopLossPips = parseFloat(lotSizeState.stopLoss)

      if (balance <= 0 || riskPercent <= 0 || stopLossPips <= 0) {
        setResult('0.00')
        return
      }

      // Pip value varies by instrument
      let pipValue = 10 // Standard for most forex pairs
      if (lotSizeState.instrument.includes('JPY')) {
        pipValue = 1000 // JPY pairs have different pip values
      } else if (lotSizeState.instrument.includes('XAU')) {
        pipValue = 100 // Gold
      } else if (lotSizeState.instrument.includes('US30')) {
        pipValue = 5 // US30
      } else if (lotSizeState.instrument.includes('SPX')) {
        pipValue = 50 // SPX500
      }

      const riskAmount = balance * riskPercent
      const lotSize = riskAmount / (stopLossPips * pipValue)

      setResult(`${lotSize.toFixed(2)}`)
    } catch (error) {
      setResult('0.00')
    }
  }

  const handleCalculate = () => {
    switch (activeTab) {
      case 'margin':
        calculateMargin()
        break
      case 'profit-loss':
        calculateProfitLoss()
        break
      case 'lot-size':
        calculateLotSize()
        break
    }
  }

  // Auto-calculate when values change
  useEffect(() => {
    if (activeTab === 'margin') {
      calculateMargin()
    }
  }, [marginState, activeTab])

  useEffect(() => {
    if (activeTab === 'profit-loss') {
      calculateProfitLoss()
    }
  }, [profitLossState, activeTab])

  useEffect(() => {
    if (activeTab === 'lot-size') {
      calculateLotSize()
    }
  }, [lotSizeState, activeTab])

  // Reset result when switching tabs
  useEffect(() => {
    setResult('$0.00')
  }, [activeTab])

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        {/* Header */}
        <div className="bg-gray-50 px-6 py-6 border-b">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3 mb-3">
            <Calculator className="w-8 h-8 text-blue-600" />
            {activeTab === 'margin' && 'Margin Calculator'}
            {activeTab === 'profit-loss' && 'Profit/Loss Calculator'}
            {activeTab === 'lot-size' && 'Lot Size Calculator'}
          </h1>
          <p className="text-gray-600 text-lg leading-relaxed">
            {activeTab === 'margin' && 'Our FundedNext Margin Calculator allows you to calculate the margin required for your trades. This tool helps you estimate the capital needed to open a position, supporting better planning and risk management for your trades.'}
            {activeTab === 'profit-loss' && 'Calculate your potential profit or loss for a trade. This tool helps you understand the financial outcome of your trading positions before you execute them.'}
            {activeTab === 'lot-size' && 'Determine the optimal lot size for your trades based on your risk management parameters. This calculator helps you maintain proper risk control in your trading strategy.'}
          </p>
        </div>

        {/* Tab Navigation */}
        <div className="border-b bg-white">
          <nav className="flex">
            <button
              onClick={() => setActiveTab('margin')}
              className={`flex-1 px-6 py-4 text-base font-medium border-b-3 transition-all duration-200 ${
                activeTab === 'margin'
                  ? 'border-blue-600 text-blue-600 bg-blue-50'
                  : 'border-transparent text-gray-600 hover:text-gray-800 hover:bg-gray-50'
              }`}
            >
              Margin Calculator
            </button>
            <button
              onClick={() => setActiveTab('profit-loss')}
              className={`flex-1 px-6 py-4 text-base font-medium border-b-3 transition-all duration-200 ${
                activeTab === 'profit-loss'
                  ? 'border-blue-600 text-blue-600 bg-blue-50'
                  : 'border-transparent text-gray-600 hover:text-gray-800 hover:bg-gray-50'
              }`}
            >
              Profit/Loss Calculator
            </button>
            <button
              onClick={() => setActiveTab('lot-size')}
              className={`flex-1 px-6 py-4 text-base font-medium border-b-3 transition-all duration-200 ${
                activeTab === 'lot-size'
                  ? 'border-blue-600 text-blue-600 bg-blue-50'
                  : 'border-transparent text-gray-600 hover:text-gray-800 hover:bg-gray-50'
              }`}
            >
              Lot Size Calculator
            </button>
          </nav>
        </div>

        {/* Calculator Content */}
        <div className="p-8">
          {activeTab === 'margin' && (
            <div className="space-y-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="space-y-2">
                  <Label htmlFor="account-model" className="text-base font-semibold text-gray-800">
                    Account Model
                  </Label>
                  <Select value={marginState.accountModel} onValueChange={(value) => setMarginState(prev => ({ ...prev, accountModel: value }))}>
                    <SelectTrigger className="w-full h-12 text-base border-2 border-gray-200 hover:border-blue-300 focus:border-blue-500">
                      <SelectValue placeholder="Select a Model" />
                    </SelectTrigger>
                    <SelectContent>
                      {accountModels.map((model) => (
                        <SelectItem key={model.value} value={model.value}>
                          {model.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="account-balance" className="text-base font-semibold text-gray-800">
                    Account Balance
                  </Label>
                  <Select value={marginState.accountBalance} onValueChange={(value) => setMarginState(prev => ({ ...prev, accountBalance: value }))}>
                    <SelectTrigger className="w-full h-12 text-base border-2 border-gray-200 hover:border-blue-300 focus:border-blue-500">
                      <SelectValue placeholder="Select a Balance" />
                    </SelectTrigger>
                    <SelectContent>
                      {accountBalances.map((balance) => (
                        <SelectItem key={balance.value} value={balance.value}>
                          {balance.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="instrument" className="text-base font-semibold text-gray-800">
                    Instrument
                  </Label>
                  <Select value={marginState.instrument} onValueChange={(value) => setMarginState(prev => ({ ...prev, instrument: value }))}>
                    <SelectTrigger className="w-full h-12 text-base border-2 border-gray-200 hover:border-blue-300 focus:border-blue-500">
                      <SelectValue placeholder="Select Instrument" />
                    </SelectTrigger>
                    <SelectContent>
                      {instruments.map((instrument) => (
                        <SelectItem key={instrument.value} value={instrument.value}>
                          {instrument.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="current-price" className="text-base font-semibold text-gray-800">
                    Current Price
                  </Label>
                  <Input
                    id="current-price"
                    type="number"
                    step="0.00001"
                    placeholder="0.00000"
                    value={marginState.currentPrice}
                    onChange={(e) => setMarginState(prev => ({ ...prev, currentPrice: e.target.value }))}
                    className="w-full h-12 text-base border-2 border-gray-200 hover:border-blue-300 focus:border-blue-500"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="lot-size" className="text-base font-semibold text-gray-800">
                    Lot Size
                  </Label>
                  <Input
                    id="lot-size"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    value={marginState.lotSize}
                    onChange={(e) => setMarginState(prev => ({ ...prev, lotSize: e.target.value }))}
                    className="w-full h-12 text-base border-2 border-gray-200 hover:border-blue-300 focus:border-blue-500"
                  />
                </div>
              </div>
            </div>
          )}

          {activeTab === 'profit-loss' && (
            <div className="space-y-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="space-y-2">
                  <Label htmlFor="account-model-pl" className="text-base font-semibold text-gray-800">
                    Account Model
                  </Label>
                  <Select value={profitLossState.accountModel} onValueChange={(value) => setProfitLossState(prev => ({ ...prev, accountModel: value }))}>
                    <SelectTrigger className="w-full h-12 text-base border-2 border-gray-200 hover:border-blue-300 focus:border-blue-500">
                      <SelectValue placeholder="Select a Model" />
                    </SelectTrigger>
                    <SelectContent>
                      {accountModels.map((model) => (
                        <SelectItem key={model.value} value={model.value}>
                          {model.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="account-balance-pl" className="text-base font-semibold text-gray-800">
                    Account Balance
                  </Label>
                  <Select value={profitLossState.accountBalance} onValueChange={(value) => setProfitLossState(prev => ({ ...prev, accountBalance: value }))}>
                    <SelectTrigger className="w-full h-12 text-base border-2 border-gray-200 hover:border-blue-300 focus:border-blue-500">
                      <SelectValue placeholder="Select a Balance" />
                    </SelectTrigger>
                    <SelectContent>
                      {accountBalances.map((balance) => (
                        <SelectItem key={balance.value} value={balance.value}>
                          {balance.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="instrument-pl" className="text-base font-semibold text-gray-800">
                    Instrument
                  </Label>
                  <Select value={profitLossState.instrument} onValueChange={(value) => setProfitLossState(prev => ({ ...prev, instrument: value }))}>
                    <SelectTrigger className="w-full h-12 text-base border-2 border-gray-200 hover:border-blue-300 focus:border-blue-500">
                      <SelectValue placeholder="Select Instrument" />
                    </SelectTrigger>
                    <SelectContent>
                      {instruments.map((instrument) => (
                        <SelectItem key={instrument.value} value={instrument.value}>
                          {instrument.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="trade-type" className="text-base font-semibold text-gray-800">
                    Trade Type
                  </Label>
                  <Select value={profitLossState.tradeType} onValueChange={(value) => setProfitLossState(prev => ({ ...prev, tradeType: value }))}>
                    <SelectTrigger className="w-full h-12 text-base border-2 border-gray-200 hover:border-blue-300 focus:border-blue-500">
                      <SelectValue placeholder="Select Trade Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="buy">Buy</SelectItem>
                      <SelectItem value="sell">Sell</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="open-price" className="text-base font-semibold text-gray-800">
                    Open Price
                  </Label>
                  <Input
                    id="open-price"
                    type="number"
                    step="0.00001"
                    placeholder="0.00000"
                    value={profitLossState.openPrice}
                    onChange={(e) => setProfitLossState(prev => ({ ...prev, openPrice: e.target.value }))}
                    className="w-full h-12 text-base border-2 border-gray-200 hover:border-blue-300 focus:border-blue-500"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="close-price" className="text-base font-semibold text-gray-800">
                    Close Price
                  </Label>
                  <Input
                    id="close-price"
                    type="number"
                    step="0.00001"
                    placeholder="0.00000"
                    value={profitLossState.closePrice}
                    onChange={(e) => setProfitLossState(prev => ({ ...prev, closePrice: e.target.value }))}
                    className="w-full h-12 text-base border-2 border-gray-200 hover:border-blue-300 focus:border-blue-500"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="lot-size-pl" className="text-base font-semibold text-gray-800">
                    Lot Size
                  </Label>
                  <Input
                    id="lot-size-pl"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    value={profitLossState.lotSize}
                    onChange={(e) => setProfitLossState(prev => ({ ...prev, lotSize: e.target.value }))}
                    className="w-full h-12 text-base border-2 border-gray-200 hover:border-blue-300 focus:border-blue-500"
                  />
                </div>
              </div>
            </div>
          )}

          {activeTab === 'lot-size' && (
            <div className="space-y-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="space-y-2">
                  <Label htmlFor="account-model-ls" className="text-base font-semibold text-gray-800">
                    Account Model
                  </Label>
                  <Select value={lotSizeState.accountModel} onValueChange={(value) => setLotSizeState(prev => ({ ...prev, accountModel: value }))}>
                    <SelectTrigger className="w-full h-12 text-base border-2 border-gray-200 hover:border-blue-300 focus:border-blue-500">
                      <SelectValue placeholder="Select a Model" />
                    </SelectTrigger>
                    <SelectContent>
                      {accountModels.map((model) => (
                        <SelectItem key={model.value} value={model.value}>
                          {model.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="account-balance-ls" className="text-base font-semibold text-gray-800">
                    Account Balance
                  </Label>
                  <Select value={lotSizeState.accountBalance} onValueChange={(value) => setLotSizeState(prev => ({ ...prev, accountBalance: value }))}>
                    <SelectTrigger className="w-full h-12 text-base border-2 border-gray-200 hover:border-blue-300 focus:border-blue-500">
                      <SelectValue placeholder="Select a Balance" />
                    </SelectTrigger>
                    <SelectContent>
                      {accountBalances.map((balance) => (
                        <SelectItem key={balance.value} value={balance.value}>
                          {balance.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="instrument-ls" className="text-base font-semibold text-gray-800">
                    Instrument
                  </Label>
                  <Select value={lotSizeState.instrument} onValueChange={(value) => setLotSizeState(prev => ({ ...prev, instrument: value }))}>
                    <SelectTrigger className="w-full h-12 text-base border-2 border-gray-200 hover:border-blue-300 focus:border-blue-500">
                      <SelectValue placeholder="Select Instrument" />
                    </SelectTrigger>
                    <SelectContent>
                      {instruments.map((instrument) => (
                        <SelectItem key={instrument.value} value={instrument.value}>
                          {instrument.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="risk-percentage" className="text-base font-semibold text-gray-800">
                    Risk Percentage (%)
                  </Label>
                  <Input
                    id="risk-percentage"
                    type="number"
                    step="0.1"
                    placeholder="2.0"
                    value={lotSizeState.riskPercentage}
                    onChange={(e) => setLotSizeState(prev => ({ ...prev, riskPercentage: e.target.value }))}
                    className="w-full h-12 text-base border-2 border-gray-200 hover:border-blue-300 focus:border-blue-500"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="stop-loss" className="text-base font-semibold text-gray-800">
                    Stop Loss (Pips)
                  </Label>
                  <Input
                    id="stop-loss"
                    type="number"
                    step="1"
                    placeholder="50"
                    value={lotSizeState.stopLoss}
                    onChange={(e) => setLotSizeState(prev => ({ ...prev, stopLoss: e.target.value }))}
                    className="w-full h-12 text-base border-2 border-gray-200 hover:border-blue-300 focus:border-blue-500"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="current-price-ls" className="text-base font-semibold text-gray-800">
                    Current Price
                  </Label>
                  <Input
                    id="current-price-ls"
                    type="number"
                    step="0.00001"
                    placeholder="0.00000"
                    value={lotSizeState.currentPrice}
                    onChange={(e) => setLotSizeState(prev => ({ ...prev, currentPrice: e.target.value }))}
                    className="w-full h-12 text-base border-2 border-gray-200 hover:border-blue-300 focus:border-blue-500"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Calculate Button */}
          <div className="flex justify-center mt-10">
            <Button
              onClick={handleCalculate}
              className="bg-blue-600 hover:bg-blue-700 text-white px-12 py-4 text-lg font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200"
            >
              Calculate
            </Button>
          </div>

          {/* Results Section */}
          <div className="mt-10 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-8 border border-blue-200">
            <h3 className="text-xl font-bold text-gray-900 mb-6">Result</h3>
            <div className={`text-5xl font-bold text-center ${
              result.includes('-') ? 'text-red-600' :
              result.includes('+') ? 'text-green-600' :
              'text-blue-600'
            }`}>
              {result}
            </div>
            {activeTab === 'lot-size' && !result.includes('$') && (
              <div className="text-center mt-2 text-gray-600">
                Lot Size
              </div>
            )}
          </div>
        </div>

        {/* Disclaimer */}
        <div className="bg-gray-100 px-8 py-6 border-t">
          <h4 className="text-xl font-bold text-gray-900 mb-3">Disclaimer</h4>
          <p className="text-base text-gray-700 leading-relaxed">
            The results from this calculator are for informational purposes only and may differ from
            actual outcomes due to market conditions. Please use caution and consider professional
            advice before making trading decisions.
          </p>
        </div>
      </div>
    </div>

  )
}