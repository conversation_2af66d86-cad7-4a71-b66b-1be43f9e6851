"use client"

import { Bad<PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Monitor, Smartphone, Tablet, Download, BarChart3, Zap, Shield, Globe } from "lucide-react"
import Image from "next/image"

export default function TradingPlatforms() {
  const platforms = [
    {
      name: "MetaTrader 4",
      description: "The world's most popular trading platform",
      logo: "https://res.cloudinary.com/dufcjjaav/image/upload/v1752130069/512x512bb-removebg-preview_qnfrll.png",
      color: "blue",
      features: [
        "Advanced charting tools",
        "30+ technical indicators",
        "Expert Advisors (EAs)",
        "One-click trading",
        "Market depth",
        "Economic calendar",
      ],
      devices: ["Windows", "Mac", "iOS", "Android", "Web"],
      popular: true,
    },
    {
      name: "MetaTrader 5",
      description: "Next-generation multi-asset platform",
      logo: "https://res.cloudinary.com/dufcjjaav/image/upload/v1752130069/MT5-removebg-preview_1_lqagz7.png",
      color: "purple",
      features: [
        "Multi-asset trading",
        "38+ technical indicators",
        "Advanced order types",
        "Market depth Level II",
        "Economic calendar",
        "MQL5 programming",
      ],
      devices: ["Windows", "Mac", "iOS", "Android", "Web"],
      popular: false,
    },
  ]

  const platformFeatures = [
    {
      title: "Lightning Fast Execution",
      description: "Sub-10ms execution speeds with no requotes",
      icon: Zap,
      color: "yellow",
    },
    {
      title: "Advanced Security",
      description: "Bank-level encryption and secure data protection",
      icon: Shield,
      color: "blue",
    },
    {
      title: "24/7 Availability",
      description: "Trade global markets around the clock",
      icon: Globe,
      color: "blue",
    },
    {
      title: "Multi-Device Access",
      description: "Trade from desktop, mobile, or web browser",
      icon: Smartphone,
      color: "purple",
    },
  ]

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: {
        bg: "from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20",
        border: "border-blue-200 dark:border-blue-400/20",
        icon: "text-blue-600 dark:text-blue-400",
        iconBg: "from-blue-500 to-cyan-500",
        button: "from-blue-600 to-cyan-600 dark:from-blue-500 dark:to-cyan-500",
      },
      purple: {
        bg: "from-purple-500/10 to-pink-500/10 dark:from-purple-500/20 dark:to-pink-500/20",
        border: "border-purple-200 dark:border-purple-400/20",
        icon: "text-purple-600 dark:text-purple-400",
        iconBg: "from-purple-500 to-pink-500",
        button: "from-purple-600 to-pink-600 dark:from-purple-500 dark:to-pink-500",
      },
      yellow: {
        bg: "from-yellow-500/10 to-orange-500/10 dark:from-yellow-500/20 dark:to-orange-500/20",
        border: "border-yellow-200 dark:border-yellow-400/20",
        icon: "text-yellow-600 dark:text-yellow-400",
        iconBg: "from-yellow-500 to-orange-500",
        button: "from-yellow-600 to-orange-600 dark:from-yellow-500 dark:to-orange-500",
      },
    }
    return colorMap[color as keyof typeof colorMap] || colorMap.blue
  }

  return (
    <section className="py-24 md:py-32 relative">
      <div className="max-w-6xl mx-auto px-8 md:px-12 lg:px-16">
        {/* Section Header */}
        <div className="mb-20 md:mb-28 max-w-4xl mx-auto text-center">
          <Badge
            variant="outline"
            className="mb-8 md:mb-12 text-sm font-light border-gray-300 dark:border-white/20 text-gray-600 dark:text-white/80 px-4 py-2 items-center"
          >
            <Monitor className="w-4 h-4 mr-2" />
            Supported Platforms
          </Badge>
          <h2 id="platforms-heading" className="text-4xl md:text-6xl lg:text-7xl font-bold mb-8 md:mb-12 leading-tight">
            Professional{" "}
            <span className="bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-400 dark:to-cyan-400 bg-clip-text text-transparent">
              trading tools
            </span>
          </h2>
          <p className="text-lg md:text-xl lg:text-2xl text-gray-700 dark:text-white/70 leading-relaxed">
            Access industry-leading trading platforms with advanced features, lightning-fast execution, and
            professional-grade tools for serious traders.
          </p>
        </div>

        {/* Platform Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 md:gap-12 mb-20">
          {platforms.map((platform, index) => {
            const colors = getColorClasses(platform.color)

            return (
              <div key={index} className="relative group">
                {platform.popular && (
                  <div className="absolute -top-4 left-1/2 -translate-x-1/2 z-20">
                    <Badge className="bg-blue-600 dark:bg-blue-500 text-white text-sm px-4 py-2 shadow-lg">
                      <BarChart3 className="w-4 h-4 mr-1" />
                      Most Popular
                    </Badge>
                  </div>
                )}

                <div
                  className={`bg-gradient-to-br ${colors.bg} backdrop-blur-sm border ${colors.border} rounded-3xl p-8 md:p-10 shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-[1.02] relative overflow-hidden h-full`}
                >
                  <div
                    className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent dark:from-white/10"
                  />

                  <div className="relative z-10 h-full flex flex-col">
                    {/* Platform Header */}
                    <div className="flex items-center gap-4 mb-6">
                      <div
                        className={`w-16 h-16 rounded-2xl bg-white dark:bg-gray-800 flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300 p-2`}
                      >
                        <Image
                          src={platform.logo}
                          alt={platform.name}
                          width={48}
                          height={48}
                          className="w-full h-full object-contain"
                        />
                      </div>
                      <div>
                        <h3 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-2">
                          {platform.name}
                        </h3>
                        <p className="text-gray-700 dark:text-white/70">{platform.description}</p>
                      </div>
                    </div>

                    {/* Features */}
                    <div className="flex-grow mb-8">
                      <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Key Features:</h4>
                      <ul className="space-y-3">
                        {platform.features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-start gap-3">
                            <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-500 to-cyan-500 mt-2 flex-shrink-0" />
                            <span className="text-sm text-gray-700 dark:text-white/70">{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Supported Devices */}
                    <div className="mb-8">
                      <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Available On:</h4>
                      <div className="flex flex-wrap gap-2">
                        {platform.devices.map((device, deviceIndex) => (
                          <Badge
                            key={deviceIndex}
                            className={`${colors.icon} bg-transparent border ${colors.border} text-xs`}
                          >
                            {device}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    {/* Download Buttons */}
                    <div className="space-y-3">
                      <Button
                        className={`w-full bg-gradient-to-r ${colors.button} text-white hover:scale-[1.02] transition-all duration-300 hover:shadow-lg py-4 text-lg font-semibold`}
                      >
                        <Download className="w-5 h-5 mr-2" />
                        Download {platform.name}
                      </Button>
                      <Button
                        variant="outline"
                        className={`w-full border-2 ${colors.border} text-gray-700 dark:text-white hover:bg-gradient-to-r ${colors.bg} transition-all duration-300 py-3 text-sm font-medium`}
                      >
                        Learn More
                      </Button>
                      <Button
                        variant="ghost"
                        className={`w-full text-gray-600 dark:text-white/60 hover:text-gray-900 dark:hover:text-white transition-all duration-300 py-2 text-xs`}
                      >
                        View Demo
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {/* Platform Features */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h3 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Why Choose Our Platforms?
            </h3>
            <p className="text-lg text-gray-700 dark:text-white/70">
              Experience superior trading conditions with institutional-grade technology
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8">
            {platformFeatures.map((feature, index) => {
              const colors = getColorClasses(feature.color)
              const IconComponent = feature.icon

              return (
                <div
                  key={index}
                  className={`bg-gradient-to-br ${colors.bg} backdrop-blur-sm border ${colors.border} rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-[1.02] group text-center`}
                >
                  <div
                    className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${colors.iconBg} flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:scale-110 transition-transform duration-300`}
                  >
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>
                  <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-3">{feature.title}</h4>
                  <p className="text-sm text-gray-700 dark:text-white/70">{feature.description}</p>
                </div>
              )
            })}
          </div>
        </div>

        {/* Device Showcase */}
        <div className="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20 backdrop-blur-sm border border-blue-200 dark:border-blue-400/20 rounded-3xl p-8 md:p-12 shadow-xl relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-cyan-500/5 dark:from-blue-500/10 dark:to-cyan-500/10" />
          <div className="relative z-10">
            <div className="text-center mb-12">
              <h3 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-4">
                Trade Anywhere, Anytime
              </h3>
              <p className="text-lg text-gray-700 dark:text-white/70 max-w-3xl mx-auto">
                Access your trading account from any device with seamless synchronization across all platforms
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="w-20 h-20 rounded-3xl bg-gradient-to-r from-blue-500/20 to-cyan-500/20 dark:from-blue-500/30 dark:to-cyan-500/30 flex items-center justify-center mx-auto mb-6">
                  <Monitor className="w-10 h-10 text-blue-600 dark:text-blue-400" />
                </div>
                <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-2">Desktop</h4>
                <p className="text-gray-700 dark:text-white/70">
                  Full-featured trading with advanced charting and analysis tools
                </p>
              </div>

              <div className="text-center">
                <div className="w-20 h-20 rounded-3xl bg-gradient-to-r from-blue-500/20 to-cyan-500/20 dark:from-blue-500/30 dark:to-cyan-500/30 flex items-center justify-center mx-auto mb-6">
                  <Smartphone className="w-10 h-10 text-blue-600 dark:text-blue-400" />
                </div>
                <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-2">Mobile</h4>
                <p className="text-gray-700 dark:text-white/70">
                  Trade on-the-go with our powerful mobile applications
                </p>
              </div>

              <div className="text-center">
                <div className="w-20 h-20 rounded-3xl bg-gradient-to-r from-blue-500/20 to-cyan-500/20 dark:from-blue-500/30 dark:to-cyan-500/30 flex items-center justify-center mx-auto mb-6">
                  <Tablet className="w-10 h-10 text-blue-600 dark:text-blue-400" />
                </div>
                <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-2">Web</h4>
                <p className="text-gray-700 dark:text-white/70">
                  Access your account from any browser with our web platform
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
