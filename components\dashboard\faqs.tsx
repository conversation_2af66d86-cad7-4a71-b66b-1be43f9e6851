"use client"

import { Bad<PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Search, MessageCircle, ArrowRight, BookOpen, Video, FileText, HelpCircle, Shield, DollarSign, Clock, Users, TrendingUp, Award, Zap, Globe } from "lucide-react"
import { useState } from "react"

interface FAQItem {
  id: number
  question: string
  answer: string
  category: string
  icon: React.ReactNode
}

const faqData: FAQItem[] = [
  {
    id: 1,
    question: "What is a prop trading challenge and how does it work?",
    answer: "A prop trading challenge is a structured evaluation program where traders demonstrate their skills using simulated capital. Successful completion leads to funding with real capital. The process involves meeting specific profit targets while adhering to risk management rules within defined timeframes. Our challenges are designed to identify consistently profitable traders who can manage risk effectively.",
    category: "General",
    icon: <HelpCircle className="w-5 h-5" />
  },
  {
    id: 2,
    question: "What are the profit targets and drawdown limits?",
    answer: "Profit targets vary by challenge type: Standard challenges require 8% profit target with 5% maximum drawdown, while Rapid challenges require 10% profit target with 6% maximum drawdown. Premium challenges offer higher targets with enhanced features. All limits are clearly defined in your challenge agreement and monitored in real-time through our advanced tracking system.",
    category: "Trading Rules",
    icon: <TrendingUp className="w-5 h-5" />
  },
  {
    id: 3,
    question: "How long do I have to complete the challenge?",
    answer: "Challenge durations are flexible and vary by program: Standard challenges typically allow 30-60 days, Rapid challenges offer 14-30 days, and Premium challenges provide extended timeframes with additional benefits. There are no strict time limits - you can take as long as needed to demonstrate consistent trading performance while meeting the required targets.",
    category: "Timeline",
    icon: <Clock className="w-5 h-5" />
  },
  {
    id: 4,
    question: "What happens after I successfully complete the challenge?",
    answer: "Upon successful challenge completion, you'll receive a funded trading account with real capital. You'll keep 90% of all profits generated, with 10% going to the platform. There are no monthly fees or hidden costs. You'll also gain access to our comprehensive support system, advanced trading tools, and community resources to maximize your success.",
    category: "Funding",
    icon: <DollarSign className="w-5 h-5" />
  },
  {
    id: 5,
    question: "What trading instruments and markets can I trade?",
    answer: "Our funded accounts provide access to major forex pairs, indices, commodities, and cryptocurrencies. You can trade during all major market sessions with competitive spreads and execution speeds. Our platform supports multiple trading strategies including scalping, day trading, and swing trading. All instruments are clearly listed in your trading dashboard with real-time pricing and analysis tools.",
    category: "Trading",
    icon: <Globe className="w-5 h-5" />
  },
  {
    id: 6,
    question: "Is there a monthly fee or subscription cost?",
    answer: "No, there are no monthly fees or subscription costs. The one-time challenge fee covers your evaluation period. Once funded, you keep 90% of profits with no ongoing charges. We believe in aligning our success with yours - we only profit when you profit. This transparent model ensures our interests are directly aligned with your trading success.",
    category: "Pricing",
    icon: <Shield className="w-5 h-5" />
  },
  {
    id: 7,
    question: "What support and resources are available to traders?",
    answer: "Our comprehensive support system includes 24/7 customer service, dedicated account managers, educational resources, trading webinars, and access to our community of funded traders. We provide advanced trading tools, market analysis, and personalized guidance to help you maximize your potential. Our team of experienced professionals is committed to your success.",
    category: "Support",
    icon: <Users className="w-5 h-5" />
  },
  {
    id: 8,
    question: "Can I withdraw my profits and how often?",
    answer: "Yes, you can withdraw your profits monthly once you've met the minimum withdrawal threshold. Withdrawals are processed within 1-3 business days to your registered payment method. There are no withdrawal fees, and you maintain full control over your profits. Our transparent profit-sharing model ensures you receive your earnings promptly and securely.",
    category: "Withdrawals",
    icon: <Award className="w-5 h-5" />
  },
  {
    id: 9,
    question: "What happens if I exceed the drawdown limit?",
    answer: "If you exceed the maximum drawdown limit, your challenge will be reset. You'll have the option to restart with a fresh evaluation period. We provide detailed feedback on your trading performance and offer guidance on risk management strategies. Our goal is to help you develop the skills needed for consistent profitability in funded trading.",
    category: "Risk Management",
    icon: <Zap className="w-5 h-5" />
  },
  {
    id: 10,
    question: "How do I get started with my trading challenge?",
    answer: "Getting started is simple: Choose your preferred challenge type and account size, complete the registration process, and you'll receive immediate access to your evaluation account. Our platform provides comprehensive onboarding, including tutorial videos, trading guidelines, and 24/7 support. You can begin trading immediately with full access to all features and tools.",
    category: "Getting Started",
    icon: <TrendingUp className="w-5 h-5" />
  }
]

const categories = ["All", "General", "Trading Rules", "Timeline", "Funding", "Trading", "Pricing", "Support", "Withdrawals", "Risk Management", "Getting Started"]

export default function FAQs() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All")

  const filteredFAQs = faqData.filter(faq => {
    const matchesSearch = faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = selectedCategory === "All" || faq.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const quickLinks = [
    {
      title: "Trading Platform Guide",
      description: "Learn how to use MetaTrader 4 and 5",
      icon: Video,
      color: "blue",
    },
    {
      title: "Risk Management",
      description: "Best practices for managing your account",
      icon: FileText,
      color: "green",
    },
    {
      title: "Challenge Rules",
      description: "Complete guide to our evaluation process",
      icon: BookOpen,
      color: "purple",
    },
  ]

  const getColorClasses = (color: string) => {
    const colorMap = {
      green: {
        bg: "from-green-500/10 to-emerald-500/10 dark:from-green-500/20 dark:to-emerald-500/20",
        border: "border-green-200 dark:border-green-400/20",
        icon: "text-green-600 dark:text-green-400",
        iconBg: "from-green-500 to-emerald-500",
      },
      blue: {
        bg: "from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20",
        border: "border-blue-200 dark:border-blue-400/20",
        icon: "text-blue-600 dark:text-blue-400",
        iconBg: "from-blue-500 to-cyan-500",
      },
      purple: {
        bg: "from-purple-500/10 to-pink-500/10 dark:from-purple-500/20 dark:to-pink-500/20",
        border: "border-purple-200 dark:border-purple-400/20",
        icon: "text-purple-600 dark:text-purple-400",
        iconBg: "from-purple-500 to-pink-500",
      },
    }
    return colorMap[color as keyof typeof colorMap] || colorMap.green
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20 backdrop-blur-sm border border-blue-200 dark:border-blue-400/20 rounded-3xl p-8 shadow-xl relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-cyan-500/5 dark:from-blue-500/10 dark:to-cyan-500/10" />
        <div className="relative z-10">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Frequently Asked Questions</h1>
          <p className="text-lg text-gray-700 dark:text-white/70 mb-6">
            Comprehensive answers to help you understand our prop trading challenges and funding program
          </p>

          {/* Search */}
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <Input
              type="text"
              placeholder="Search FAQs..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="rounded-xl border-gray-200 dark:border-white/20 bg-white/50 dark:bg-white/5 pl-10"
            />
          </div>
        </div>
      </div>

      {/* Category Filter */}
      <div className="flex flex-wrap justify-center gap-2 mb-6">
        {categories.map((category) => (
          <Button
            key={category}
            variant={selectedCategory === category ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedCategory(category)}
            className={`rounded-full px-4 py-2 text-sm font-medium transition-all duration-300 ${
              selectedCategory === category
                ? "bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg"
                : "border-gray-300 dark:border-gray-600 text-gray-700 dark:text-white/70 hover:bg-gray-100 dark:hover:bg-gray-800"
            }`}
          >
            {category}
          </Button>
        ))}
      </div>

      {/* Quick Links */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {quickLinks.map((link, index) => {
          const colors = getColorClasses(link.color)
          const IconComponent = link.icon

          return (
            <div
              key={index}
              className={`bg-gradient-to-br ${colors.bg} backdrop-blur-sm border ${colors.border} rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-[1.02] group cursor-pointer`}
            >
              <div className="flex items-center gap-4 mb-4">
                <div
                  className={`w-12 h-12 rounded-2xl bg-gradient-to-r ${colors.iconBg} flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`}
                >
                  <IconComponent className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white">{link.title}</h3>
                </div>
              </div>
              <p className="text-gray-700 dark:text-white/70 mb-4">{link.description}</p>
              <Button
                variant="ghost"
                size="sm"
                className={`${colors.icon} hover:bg-white/20 dark:hover:bg-white/10 p-0`}
              >
                Learn More
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          )
        })}
      </div>

      {/* FAQ Items */}
      <div className="space-y-4">
        {filteredFAQs.map((faq) => (
          <div
            key={faq.id}
            className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300"
          >
            <Accordion type="single" collapsible>
              <AccordionItem value={`faq-${faq.id}`} className="border-none">
                <AccordionTrigger className="text-left hover:no-underline group">
                  <div className="flex items-center gap-4">
                    <div className="text-blue-600 dark:text-blue-400">
                      {faq.icon}
                </div>
                    <div className="text-left">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                        {faq.question}
                      </h3>
                      <span className="text-xs text-blue-600 dark:text-blue-400 font-medium">
                        {faq.category}
                      </span>
                    </div>
                  </div>
                    </AccordionTrigger>
                    <AccordionContent className="pt-4 pb-2">
                  <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                      <p className="text-base text-gray-700 dark:text-white/70 leading-relaxed">{faq.answer}</p>
                  </div>
                    </AccordionContent>
                  </AccordionItem>
              </Accordion>
            </div>
        ))}
      </div>

      {/* No Results Message */}
      {filteredFAQs.length === 0 && (
        <div className="text-center py-12">
          <div className="w-16 h-16 rounded-2xl bg-gradient-to-r from-gray-500 to-gray-600 flex items-center justify-center mx-auto mb-6 shadow-lg">
            <Search className="w-8 h-8 text-white" />
          </div>
          <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">No FAQs Found</h3>
          <p className="text-gray-600 dark:text-white/70">
            Try adjusting your search terms or category filter
          </p>
        </div>
      )}

      {/* Contact Support */}
      <div className="bg-gradient-to-r from-green-500/10 to-emerald-500/10 dark:from-green-500/20 dark:to-emerald-500/20 backdrop-blur-sm border border-green-200 dark:border-green-400/20 rounded-3xl p-8 shadow-xl">
        <div className="text-center">
          <div className="w-16 h-16 rounded-2xl bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center mx-auto mb-6 shadow-lg">
            <MessageCircle className="w-8 h-8 text-white" />
          </div>
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Still have questions?</h3>
          <p className="text-lg text-gray-700 dark:text-white/70 mb-8 max-w-2xl mx-auto">
            Our support team is available 24/7 to help you with any questions about trading, challenges, or account
            management.
          </p>
          <Button className="rounded-full bg-gradient-to-r from-green-600 to-emerald-600 dark:from-green-500 dark:to-emerald-500 text-white hover:from-green-700 hover:to-emerald-700 dark:hover:from-green-600 dark:hover:to-emerald-600 px-8 py-4 text-lg hover:scale-105 transition-all duration-300 hover:shadow-lg">
            Contact Support
            <MessageCircle className="ml-2 h-5 w-5" />
          </Button>
        </div>
      </div>
    </div>
  )
}


