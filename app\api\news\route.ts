import { NextResponse } from 'next/server'

interface NewsArticle {
  id: string
  title: string
  summary: string
  content: string
  url: string
  source: string
  author?: string
  publishedAt: string
  category: string
  sentiment: 'bullish' | 'bearish' | 'neutral'
  impact: 'high' | 'medium' | 'low'
  symbols?: string[]
  imageUrl?: string
  readTime?: number
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category') || 'all'
    const limit = parseInt(searchParams.get('limit') || '20')
    const source = searchParams.get('source') || 'all'

    let allNews: NewsArticle[] = []

    // 1. Try Alpha Vantage News API (Free tier available)
    const ALPHA_VANTAGE_API_KEY = process.env.ALPHA_VANTAGE_API_KEY
    if (ALPHA_VANTAGE_API_KEY) {
      try {
        const topics = category === 'all' ? 'financial_markets' : category
        const response = await fetch(
          `https://www.alphavantage.co/query?function=NEWS_SENTIMENT&topics=${topics}&apikey=${ALPHA_VANTAGE_API_KEY}&limit=${Math.min(limit, 50)}`
        )
        
        if (response.ok) {
          const data = await response.json()
          if (data.feed && Array.isArray(data.feed)) {
            const alphaVantageNews = data.feed.map((article: any, index: number) => ({
              id: `av-${article.time_published || index}`,
              title: article.title || 'No title',
              summary: article.summary || article.title || 'No summary available',
              content: article.summary || article.title || 'No content available',
              url: article.url || '#',
              source: article.source || 'Alpha Vantage',
              author: article.authors?.[0] || 'Unknown',
              publishedAt: article.time_published ? 
                new Date(article.time_published.replace(/(\d{4})(\d{2})(\d{2})T(\d{2})(\d{2})(\d{2})/, '$1-$2-$3T$4:$5:$6')).toISOString() :
                new Date().toISOString(),
              category: article.topics?.[0]?.topic || 'general',
              sentiment: article.overall_sentiment_label?.toLowerCase() === 'bullish' ? 'bullish' :
                        article.overall_sentiment_label?.toLowerCase() === 'bearish' ? 'bearish' : 'neutral',
              impact: article.overall_sentiment_score > 0.15 ? 'high' :
                     article.overall_sentiment_score > 0.05 ? 'medium' : 'low',
              symbols: article.ticker_sentiment?.map((t: any) => t.ticker) || [],
              imageUrl: article.banner_image || undefined,
              readTime: Math.ceil((article.summary?.length || 500) / 200)
            }))
            allNews.push(...alphaVantageNews)
          }
        }
      } catch (error) {
        console.error('Alpha Vantage News API error:', error)
      }
    }

    // 2. Try Polygon.io News API (Free tier available)
    const POLYGON_API_KEY = process.env.POLYGON_API_KEY
    if (POLYGON_API_KEY && allNews.length < limit) {
      try {
        const response = await fetch(
          `https://api.polygon.io/v2/reference/news?limit=${Math.min(limit - allNews.length, 50)}&apikey=${POLYGON_API_KEY}`
        )
        
        if (response.ok) {
          const data = await response.json()
          if (data.results && Array.isArray(data.results)) {
            const polygonNews = data.results.map((article: any) => ({
              id: `polygon-${article.id || Date.now()}`,
              title: article.title || 'No title',
              summary: article.description || article.title || 'No summary available',
              content: article.description || article.title || 'No content available',
              url: article.article_url || '#',
              source: article.publisher?.name || 'Polygon.io',
              author: article.author || 'Unknown',
              publishedAt: article.published_utc || new Date().toISOString(),
              category: 'financial_markets',
              sentiment: 'neutral' as const,
              impact: 'medium' as const,
              symbols: article.tickers || [],
              imageUrl: article.image_url || undefined,
              readTime: Math.ceil((article.description?.length || 500) / 200)
            }))
            allNews.push(...polygonNews)
          }
        }
      } catch (error) {
        console.error('Polygon.io News API error:', error)
      }
    }

    // 3. Try NewsAPI for general financial news (Free tier available)
    const NEWS_API_KEY = process.env.NEWS_API_KEY
    if (NEWS_API_KEY && allNews.length < limit) {
      try {
        const query = category === 'all' ? 'forex OR trading OR financial markets' : category
        const response = await fetch(
          `https://newsapi.org/v2/everything?q=${encodeURIComponent(query)}&domains=reuters.com,bloomberg.com,cnbc.com,marketwatch.com,investing.com&sortBy=publishedAt&pageSize=${Math.min(limit - allNews.length, 50)}&apiKey=${NEWS_API_KEY}`
        )
        
        if (response.ok) {
          const data = await response.json()
          if (data.articles && Array.isArray(data.articles)) {
            const newsApiArticles = data.articles
              .filter((article: any) => article.title && article.title !== '[Removed]')
              .map((article: any) => ({
                id: `newsapi-${article.publishedAt}-${article.title.slice(0, 20).replace(/\s+/g, '-')}`,
                title: article.title,
                summary: article.description || article.title,
                content: article.content || article.description || article.title,
                url: article.url,
                source: article.source?.name || 'NewsAPI',
                author: article.author || 'Unknown',
                publishedAt: article.publishedAt,
                category: 'financial_markets',
                sentiment: 'neutral' as const,
                impact: 'medium' as const,
                symbols: [],
                imageUrl: article.urlToImage || undefined,
                readTime: Math.ceil((article.content?.length || 500) / 200)
              }))
            allNews.push(...newsApiArticles)
          }
        }
      } catch (error) {
        console.error('NewsAPI error:', error)
      }
    }

    // 4. Try Financial Modeling Prep News API (Free tier available)
    const FMP_API_KEY = process.env.FMP_API_KEY
    if (FMP_API_KEY && allNews.length < limit) {
      try {
        const response = await fetch(
          `https://financialmodelingprep.com/api/v3/fmp/articles?page=0&size=${Math.min(limit - allNews.length, 50)}&apikey=${FMP_API_KEY}`
        )
        
        if (response.ok) {
          const data = await response.json()
          if (Array.isArray(data)) {
            const fmpNews = data.map((article: any) => ({
              id: `fmp-${article.publishedDate}-${article.title?.slice(0, 20).replace(/\s+/g, '-')}`,
              title: article.title || 'No title',
              summary: article.content?.substring(0, 200) + '...' || article.title,
              content: article.content || article.title,
              url: article.url || '#',
              source: article.site || 'FMP',
              author: 'Financial Modeling Prep',
              publishedAt: article.publishedDate || new Date().toISOString(),
              category: 'financial_markets',
              sentiment: 'neutral' as const,
              impact: 'medium' as const,
              symbols: [],
              imageUrl: article.image || undefined,
              readTime: Math.ceil((article.content?.length || 500) / 200)
            }))
            allNews.push(...fmpNews)
          }
        }
      } catch (error) {
        console.error('FMP News API error:', error)
      }
    }

    // 5. Try Finnhub News API (Free tier available)
    const FINNHUB_API_KEY = process.env.FINNHUB_API_KEY
    if (FINNHUB_API_KEY && allNews.length < limit) {
      try {
        const response = await fetch(
          `https://finnhub.io/api/v1/news?category=general&token=${FINNHUB_API_KEY}`
        )

        if (response.ok) {
          const data = await response.json()
          if (Array.isArray(data)) {
            const finnhubNews = data.slice(0, Math.min(limit - allNews.length, 50)).map((article: any) => ({
              id: `finnhub-${article.id || article.datetime}`,
              title: article.headline || 'No title',
              summary: article.summary || article.headline || 'No summary available',
              content: article.summary || article.headline || 'No content available',
              url: article.url || '#',
              source: article.source || 'Finnhub',
              author: 'Finnhub',
              publishedAt: new Date(article.datetime * 1000).toISOString(),
              category: article.category || 'general',
              sentiment: 'neutral' as const,
              impact: 'medium' as const,
              symbols: article.related ? [article.related] : [],
              imageUrl: article.image || undefined,
              readTime: Math.ceil((article.summary?.length || 500) / 200)
            }))
            allNews.push(...finnhubNews)
          }
        }
      } catch (error) {
        console.error('Finnhub News API error:', error)
      }
    }

    // 6. Try TradingView News (via RSS/Web scraping alternative)
    if (allNews.length < limit) {
      try {
        // TradingView Ideas RSS feed (public)
        const response = await fetch('https://www.tradingview.com/ideas/rss/')

        if (response.ok) {
          const rssText = await response.text()
          // Simple RSS parsing for TradingView ideas
          const items = rssText.match(/<item>[\s\S]*?<\/item>/g) || []

          const tradingViewNews = items.slice(0, Math.min(limit - allNews.length, 10)).map((item, index) => {
            const title = item.match(/<title><!\[CDATA\[(.*?)\]\]><\/title>/)?.[1] || 'TradingView Idea'
            const description = item.match(/<description><!\[CDATA\[(.*?)\]\]><\/description>/)?.[1] || ''
            const link = item.match(/<link>(.*?)<\/link>/)?.[1] || '#'
            const pubDate = item.match(/<pubDate>(.*?)<\/pubDate>/)?.[1] || new Date().toISOString()

            return {
              id: `tradingview-${Date.now()}-${index}`,
              title: title,
              summary: description.substring(0, 200) + '...',
              content: description,
              url: link,
              source: 'TradingView',
              author: 'TradingView Community',
              publishedAt: new Date(pubDate).toISOString(),
              category: 'technical_analysis',
              sentiment: 'neutral' as const,
              impact: 'medium' as const,
              symbols: [],
              readTime: Math.ceil((description?.length || 500) / 200)
            }
          })
          allNews.push(...tradingViewNews)
        }
      } catch (error) {
        console.error('TradingView RSS error:', error)
      }
    }

    // 7. Fallback: If no real data available, provide sample data with disclaimer
    if (allNews.length === 0) {
      const fallbackNews: NewsArticle[] = [
        {
          id: 'fallback-1',
          title: 'Configure API Keys for Real Trading News',
          summary: 'To get real-time trading news, please configure your API keys in the environment variables.',
          content: 'This is a placeholder article. To get real trading news from TradingView, Alpha Vantage, and other sources, please add your API keys to the .env.local file.',
          url: '#',
          source: 'System',
          author: 'Lunch Box Trading',
          publishedAt: new Date().toISOString(),
          category: 'system',
          sentiment: 'neutral',
          impact: 'low',
          symbols: [],
          readTime: 1
        },
        {
          id: 'fallback-2',
          title: 'Real-Time Market Analysis Coming Soon',
          summary: 'Professional trading news and analysis will be available once API integration is complete.',
          content: 'We are working to bring you the latest market news from trusted sources including TradingView, Bloomberg, Reuters, and more.',
          url: '#',
          source: 'System',
          author: 'Lunch Box Trading',
          publishedAt: new Date(Date.now() - 3600000).toISOString(),
          category: 'system',
          sentiment: 'neutral',
          impact: 'low',
          symbols: [],
          readTime: 1
        }
      ]
      allNews = fallbackNews
    }
    
    // Sort by published date (newest first)
    allNews.sort((a, b) => new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime())

    // Remove duplicates based on title similarity
    const uniqueNews = allNews.filter((article, index, self) => 
      index === self.findIndex(a => 
        a.title.toLowerCase().trim() === article.title.toLowerCase().trim()
      )
    )

    // Apply filters
    let filteredNews = uniqueNews
    if (category !== 'all') {
      filteredNews = filteredNews.filter(article => 
        article.category.toLowerCase().includes(category.toLowerCase())
      )
    }

    if (source !== 'all') {
      filteredNews = filteredNews.filter(article => 
        article.source.toLowerCase().includes(source.toLowerCase())
      )
    }

    // Limit results
    const finalNews = filteredNews.slice(0, limit)

    return NextResponse.json({
      success: true,
      data: finalNews,
      total: finalNews.length,
      timestamp: new Date().toISOString(),
      sources: [...new Set(finalNews.map(article => article.source))],
      categories: [...new Set(finalNews.map(article => article.category))]
    })

  } catch (error) {
    console.error('News API error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch news data',
        message: 'Please check your API configuration'
      },
      { status: 500 }
    )
  }
}
