"use client"

import { <PERSON><PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { X, Zap, Clock, ArrowRight } from "lucide-react"
import { useState } from "react"

export default function SaleBanner() {
  const [isVisible, setIsVisible] = useState(true)

  if (!isVisible) return null

  return (
    <div className="relative bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-500 dark:to-cyan-500 text-white py-4 px-6 shadow-lg">
      <div className="absolute inset-0 bg-gradient-to-r from-blue-600/90 to-cyan-600/90 dark:from-blue-500/90 dark:to-cyan-500/90" />

      <div className="relative z-10 max-w-6xl mx-auto flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 rounded-full bg-white/20 flex items-center justify-center animate-pulse">
              <Zap className="w-4 h-4 text-white" />
            </div>
            <Badge className="bg-white/20 text-white border-white/30 text-xs px-2 py-1">LIMITED TIME</Badge>
          </div>

          <div className="flex items-center gap-6">
            <div className="text-sm md:text-base font-semibold">
              🎉 <span className="font-bold">50% OFF</span> All Trading Challenges - New Year Special!
            </div>

            <div className="hidden md:flex items-center gap-2 text-sm">
              <Clock className="w-4 h-4" />
              <span>Ends in 3 days</span>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <Button size="sm" className="bg-white text-blue-600 hover:bg-white/90 font-semibold px-4 py-2 text-sm">
            Claim Offer
            <ArrowRight className="w-4 h-4 ml-1" />
          </Button>

          <button
            onClick={() => setIsVisible(false)}
            className="w-6 h-6 rounded-full bg-white/20 hover:bg-white/30 flex items-center justify-center transition-colors duration-200"
            aria-label="Close banner"
          >
            <X className="w-4 h-4 text-white" />
          </button>
        </div>
      </div>
    </div>
  )
}
