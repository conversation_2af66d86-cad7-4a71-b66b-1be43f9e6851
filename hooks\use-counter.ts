import { useState, useEffect } from 'react'

interface UseCounterProps {
  end: number
  start?: number
  duration?: number
  delay?: number
  suffix?: string
  prefix?: string
}

export const useCounter = ({ 
  end, 
  start = 0, 
  duration = 2000, 
  delay = 0,
  suffix = '',
  prefix = ''
}: UseCounterProps) => {
  const [count, setCount] = useState(start)

  useEffect(() => {
    const timer = setTimeout(() => {
      const startTime = Date.now()
      const endTime = startTime + duration

      const updateCount = () => {
        const now = Date.now()
        const progress = Math.min((now - startTime) / duration, 1)
        
        // Easing function for smooth animation
        const easeOutQuart = 1 - Math.pow(1 - progress, 4)
        const currentCount = Math.floor(start + (end - start) * easeOutQuart)
        
        setCount(currentCount)

        if (progress < 1) {
          requestAnimationFrame(updateCount)
        }
      }

      requestAnimationFrame(updateCount)
    }, delay)

    return () => clearTimeout(timer)
  }, [end, start, duration, delay])

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${prefix}${(num / 1000000).toFixed(1)}M${suffix}`
    } else if (num >= 1000) {
      return `${prefix}${(num / 1000).toFixed(1)}K${suffix}`
    }
    return `${prefix}${num}${suffix}`
  }

  return { count, formattedCount: formatNumber(count) }
} 