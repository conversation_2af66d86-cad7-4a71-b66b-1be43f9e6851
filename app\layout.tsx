import { LanguageProvider } from '@/contexts/language-context'
import { AuthProvider } from '@/contexts/auth-context'
import { KYCProvider } from '@/contexts/kyc-context'
import { ThemeProvider } from '@/components/theme-provider'
import { Toaster } from '@/components/ui/sonner'
import './globals.css'

export const metadata = {
  title: 'FxThrone - Trading Challenge & Prop Firm Evaluation',
  description: 'Join FxThrone\'s simulated trading challenges and skill-based evaluations. Access performance-based funding opportunities through our educational forex evaluation program.',
  keywords: 'trading challenge, prop firm evaluation, forex evaluation program, trader funding program, simulated trading account, trading skill assessment',
  icons: {
    icon: [
      { url: '/favicon.ico', sizes: 'any' },
      { url: '/favicon.ico', type: 'image/x-icon' },
    ],
    shortcut: '/favicon.ico',
    apple: '/apple-touch-icon.png',
  },
  openGraph: {
    title: 'FxThrone - Trading Challenge & Prop Firm Evaluation',
    description: 'Join <PERSON>x<PERSON>hrone\'s simulated trading challenges and skill-based evaluations. Access performance-based funding opportunities through our educational forex evaluation program.',
    type: 'website',
    locale: 'en_US',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'FxThrone - Trading Challenge & Prop Firm Evaluation',
    description: 'Join FxThrone\'s simulated trading challenges and skill-based evaluations. Access performance-based funding opportunities through our educational forex evaluation program.',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body suppressHydrationWarning>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <AuthProvider>
            <KYCProvider>
              <LanguageProvider>
                {children}
                <Toaster />
              </LanguageProvider>
            </KYCProvider>
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
