"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Checkbox } from "@/components/ui/checkbox"
import { Mail, Lock, Eye, EyeOff, Phone, User, TrendingUp, Shield, Clock, DollarSign, Users, Award, Globe, CheckCircle, ArrowRight, Zap, Star, Target, Gift, Newspaper, LogIn, Moon, Sun } from "lucide-react"
import { useAuth } from "@/contexts/auth-context"
import { useLanguage } from "@/contexts/language-context"
import LanguageSelector from "@/components/language-selector"
import Footer from "@/components/footer"
import PhoneInput from "react-phone-number-input"
import "react-phone-number-input/style.css"
import ReactSelect from "react-select"
import { useSearchParams } from "next/navigation"
import { toast } from "sonner"
import Navbar from "@/components/navbar"
import Link from "next/link"

export default function AuthPage() {
  const [activeTab, setActiveTab] = useState("login")
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")
  const [isDarkMode, setIsDarkMode] = useState(true)
  const { t, isRTL } = useLanguage()
  const { login, signup, isLoading } = useAuth()
  const searchParams = useSearchParams()

  const [formData, setFormData] = useState({
    email: "",
    password: "",
    confirmPassword: "",
    firstName: "",
    lastName: "",
    phone: "",
    country: "",
    address: "",
    referral_code: ""
  })

  // Country data with flags
  const countries = [
    { value: "US", label: "United States", flag: "🇺🇸" },
    { value: "GB", label: "United Kingdom", flag: "🇬🇧" },
    { value: "CA", label: "Canada", flag: "🇨🇦" },
    { value: "AU", label: "Australia", flag: "🇦🇺" },
    { value: "DE", label: "Germany", flag: "🇩🇪" },
    { value: "FR", label: "France", flag: "🇫🇷" },
    { value: "JP", label: "Japan", flag: "🇯🇵" },
    { value: "IN", label: "India", flag: "🇮🇳" },
    { value: "BR", label: "Brazil", flag: "🇧🇷" },
    { value: "IT", label: "Italy", flag: "🇮🇹" },
    { value: "ES", label: "Spain", flag: "🇪🇸" },
    { value: "NL", label: "Netherlands", flag: "🇳🇱" },
    { value: "SE", label: "Sweden", flag: "🇸🇪" },
    { value: "NO", label: "Norway", flag: "🇳🇴" },
    { value: "DK", label: "Denmark", flag: "🇩🇰" },
    { value: "FI", label: "Finland", flag: "🇫🇮" },
    { value: "CH", label: "Switzerland", flag: "🇨🇭" },
    { value: "AT", label: "Austria", flag: "🇦🇹" },
    { value: "BE", label: "Belgium", flag: "🇧🇪" },
    { value: "IE", label: "Ireland", flag: "🇮🇪" },
    { value: "NZ", label: "New Zealand", flag: "🇳🇿" },
    { value: "SG", label: "Singapore", flag: "🇸🇬" },
    { value: "HK", label: "Hong Kong", flag: "🇭🇰" },
    { value: "KR", label: "South Korea", flag: "🇰🇷" },
    { value: "CN", label: "China", flag: "🇨🇳" },
    { value: "RU", label: "Russia", flag: "🇷🇺" },
    { value: "PL", label: "Poland", flag: "🇵🇱" },
    { value: "CZ", label: "Czech Republic", flag: "🇨🇿" },
    { value: "HU", label: "Hungary", flag: "🇭🇺" },
    { value: "RO", label: "Romania", flag: "🇷🇴" },
    { value: "BG", label: "Bulgaria", flag: "🇧🇬" },
    { value: "HR", label: "Croatia", flag: "🇭🇷" },
    { value: "SI", label: "Slovenia", flag: "🇸🇮" },
    { value: "SK", label: "Slovakia", flag: "🇸🇰" },
    { value: "LT", label: "Lithuania", flag: "🇱🇹" },
    { value: "LV", label: "Latvia", flag: "🇱🇻" },
    { value: "EE", label: "Estonia", flag: "🇪🇪" },
    { value: "MT", label: "Malta", flag: "🇲🇹" },
    { value: "CY", label: "Cyprus", flag: "🇨🇾" },
    { value: "GR", label: "Greece", flag: "🇬🇷" },
    { value: "PT", label: "Portugal", flag: "🇵🇹" },
    { value: "MX", label: "Mexico", flag: "🇲🇽" },
    { value: "AR", label: "Argentina", flag: "🇦🇷" },
    { value: "CL", label: "Chile", flag: "🇨🇱" },
    { value: "CO", label: "Colombia", flag: "🇨🇴" },
    { value: "PE", label: "Peru", flag: "🇵🇪" },
    { value: "VE", label: "Venezuela", flag: "🇻🇪" },
    { value: "UY", label: "Uruguay", flag: "🇺🇾" },
    { value: "PY", label: "Paraguay", flag: "🇵🇾" },
    { value: "BO", label: "Bolivia", flag: "🇧🇴" },
    { value: "EC", label: "Ecuador", flag: "🇪🇨" },
    { value: "GT", label: "Guatemala", flag: "🇬🇹" },
    { value: "SV", label: "El Salvador", flag: "🇸🇻" },
    { value: "HN", label: "Honduras", flag: "🇭🇳" },
    { value: "NI", label: "Nicaragua", flag: "🇳🇮" },
    { value: "CR", label: "Costa Rica", flag: "🇨🇷" },
    { value: "PA", label: "Panama", flag: "🇵🇦" },
    { value: "CU", label: "Cuba", flag: "🇨🇺" },
    { value: "JM", label: "Jamaica", flag: "🇯🇲" },
    { value: "TT", label: "Trinidad and Tobago", flag: "🇹🇹" },
    { value: "BB", label: "Barbados", flag: "🇧🇧" },
    { value: "GD", label: "Grenada", flag: "🇬🇩" },
    { value: "LC", label: "Saint Lucia", flag: "🇱🇨" },
    { value: "VC", label: "Saint Vincent and the Grenadines", flag: "🇻🇨" },
    { value: "AG", label: "Antigua and Barbuda", flag: "🇦🇬" },
    { value: "KN", label: "Saint Kitts and Nevis", flag: "🇰🇳" },
    { value: "DM", label: "Dominica", flag: "🇩🇲" },
    { value: "DO", label: "Dominican Republic", flag: "🇩🇴" },
    { value: "HT", label: "Haiti", flag: "🇭🇹" },
    { value: "BH", label: "Bahrain", flag: "🇧🇭" },
    { value: "KW", label: "Kuwait", flag: "🇰🇼" },
    { value: "OM", label: "Oman", flag: "🇴🇲" },
    { value: "QA", label: "Qatar", flag: "🇶🇦" },
    { value: "SA", label: "Saudi Arabia", flag: "🇸🇦" },
    { value: "AE", label: "United Arab Emirates", flag: "🇦🇪" },
    { value: "JO", label: "Jordan", flag: "🇯🇴" },
    { value: "LB", label: "Lebanon", flag: "🇱🇧" },
    { value: "SY", label: "Syria", flag: "🇸🇾" },
    { value: "IQ", label: "Iraq", flag: "🇮🇶" },
    { value: "IR", label: "Iran", flag: "🇮🇷" },
    { value: "AF", label: "Afghanistan", flag: "🇦🇫" },
    { value: "PK", label: "Pakistan", flag: "🇵🇰" },
    { value: "BD", label: "Bangladesh", flag: "🇧🇩" },
    { value: "LK", label: "Sri Lanka", flag: "🇱🇰" },
    { value: "NP", label: "Nepal", flag: "🇳🇵" },
    { value: "BT", label: "Bhutan", flag: "🇧🇹" },
    { value: "MV", label: "Maldives", flag: "🇲🇻" },
    { value: "MM", label: "Myanmar", flag: "🇲🇲" },
    { value: "TH", label: "Thailand", flag: "🇹🇭" },
    { value: "VN", label: "Vietnam", flag: "🇻🇳" },
    { value: "LA", label: "Laos", flag: "🇱🇦" },
    { value: "KH", label: "Cambodia", flag: "🇰🇭" },
    { value: "MY", label: "Malaysia", flag: "🇲🇾" },
    { value: "ID", label: "Indonesia", flag: "🇮🇩" },
    { value: "PH", label: "Philippines", flag: "🇵🇭" },
    { value: "BN", label: "Brunei", flag: "🇧🇳" },
    { value: "TL", label: "East Timor", flag: "🇹🇱" },
    { value: "KH", label: "Cambodia", flag: "🇰🇭" },
    { value: "MN", label: "Mongolia", flag: "🇲🇳" },
    { value: "KZ", label: "Kazakhstan", flag: "🇰🇿" },
    { value: "UZ", label: "Uzbekistan", flag: "🇺🇿" },
    { value: "KG", label: "Kyrgyzstan", flag: "🇰🇬" },
    { value: "TJ", label: "Tajikistan", flag: "🇹🇯" },
    { value: "TM", label: "Turkmenistan", flag: "🇹🇲" },
    { value: "AZ", label: "Azerbaijan", flag: "🇦🇿" },
    { value: "GE", label: "Georgia", flag: "🇬🇪" },
    { value: "AM", label: "Armenia", flag: "🇦🇲" },
    { value: "TR", label: "Turkey", flag: "🇹🇷" },
    { value: "IL", label: "Israel", flag: "🇮🇱" },
    { value: "PS", label: "Palestine", flag: "🇵🇸" },
    { value: "EG", label: "Egypt", flag: "🇪🇬" },
    { value: "LY", label: "Libya", flag: "🇱🇾" },
    { value: "TN", label: "Tunisia", flag: "🇹🇳" },
    { value: "DZ", label: "Algeria", flag: "🇩🇿" },
    { value: "MA", label: "Morocco", flag: "🇲🇦" },
    { value: "SD", label: "Sudan", flag: "🇸🇩" },
    { value: "SS", label: "South Sudan", flag: "🇸🇸" },
    { value: "ET", label: "Ethiopia", flag: "🇪🇹" },
    { value: "ER", label: "Eritrea", flag: "🇪🇷" },
    { value: "DJ", label: "Djibouti", flag: "🇩🇯" },
    { value: "SO", label: "Somalia", flag: "🇸🇴" },
    { value: "KE", label: "Kenya", flag: "🇰🇪" },
    { value: "TZ", label: "Tanzania", flag: "🇹🇿" },
    { value: "UG", label: "Uganda", flag: "🇺🇬" },
    { value: "RW", label: "Rwanda", flag: "🇷🇼" },
    { value: "BI", label: "Burundi", flag: "🇧🇮" },
    { value: "CD", label: "Democratic Republic of the Congo", flag: "🇨🇩" },
    { value: "CG", label: "Republic of the Congo", flag: "🇨🇬" },
    { value: "CF", label: "Central African Republic", flag: "🇨🇫" },
    { value: "CM", label: "Cameroon", flag: "🇨🇲" },
    { value: "GQ", label: "Equatorial Guinea", flag: "🇬🇶" },
    { value: "GA", label: "Gabon", flag: "🇬🇦" },
    { value: "ST", label: "São Tomé and Príncipe", flag: "🇸🇹" },
    { value: "AO", label: "Angola", flag: "🇦🇴" },
    { value: "NA", label: "Namibia", flag: "🇳🇦" },
    { value: "BW", label: "Botswana", flag: "🇧🇼" },
    { value: "ZW", label: "Zimbabwe", flag: "🇿🇼" },
    { value: "ZM", label: "Zambia", flag: "🇿🇲" },
    { value: "MW", label: "Malawi", flag: "🇲🇼" },
    { value: "MZ", label: "Mozambique", flag: "🇲🇿" },
    { value: "MG", label: "Madagascar", flag: "🇲🇬" },
    { value: "MU", label: "Mauritius", flag: "🇲🇺" },
    { value: "SC", label: "Seychelles", flag: "🇸🇨" },
    { value: "KM", label: "Comoros", flag: "🇰🇲" },
    { value: "ZA", label: "South Africa", flag: "🇿🇦" },
    { value: "LS", label: "Lesotho", flag: "🇱🇸" },
    { value: "SZ", label: "Eswatini", flag: "🇸🇿" },
    { value: "MG", label: "Madagascar", flag: "🇲🇬" },
    { value: "RE", label: "Réunion", flag: "🇷🇪" },
    { value: "YT", label: "Mayotte", flag: "🇾🇹" },
    { value: "SH", label: "Saint Helena", flag: "🇸🇭" },
    { value: "FK", label: "Falkland Islands", flag: "🇫🇰" },
    { value: "GS", label: "South Georgia and the South Sandwich Islands", flag: "🇬🇸" },
    { value: "BV", label: "Bouvet Island", flag: "🇧🇻" },
    { value: "AQ", label: "Antarctica", flag: "🇦🇶" },
    { value: "TF", label: "French Southern Territories", flag: "🇹🇫" },
    { value: "HM", label: "Heard Island and McDonald Islands", flag: "🇭🇲" },
    { value: "IO", label: "British Indian Ocean Territory", flag: "🇮🇴" },
    { value: "CX", label: "Christmas Island", flag: "🇨🇽" },
    { value: "CC", label: "Cocos (Keeling) Islands", flag: "🇨🇨" },
    { value: "NF", label: "Norfolk Island", flag: "🇳🇫" },
    { value: "TK", label: "Tokelau", flag: "🇹🇰" },
    { value: "NU", label: "Niue", flag: "🇳🇺" },
    { value: "CK", label: "Cook Islands", flag: "🇨🇰" },
    { value: "WS", label: "Samoa", flag: "🇼🇸" },
    { value: "TO", label: "Tonga", flag: "🇹🇴" },
    { value: "FJ", label: "Fiji", flag: "🇫🇯" },
    { value: "VU", label: "Vanuatu", flag: "🇻🇺" },
    { value: "NC", label: "New Caledonia", flag: "🇳🇨" },
    { value: "PF", label: "French Polynesia", flag: "🇵🇫" },
    { value: "WF", label: "Wallis and Futuna", flag: "🇼🇫" },
    { value: "AS", label: "American Samoa", flag: "🇦🇸" },
    { value: "GU", label: "Guam", flag: "🇬🇺" },
    { value: "MP", label: "Northern Mariana Islands", flag: "🇲🇵" },
    { value: "PW", label: "Palau", flag: "🇵🇼" },
    { value: "FM", label: "Micronesia", flag: "🇫🇲" },
    { value: "MH", label: "Marshall Islands", flag: "🇲🇭" },
    { value: "KI", label: "Kiribati", flag: "🇰🇮" },
    { value: "TV", label: "Tuvalu", flag: "🇹🇻" },
    { value: "NR", label: "Nauru", flag: "🇳🇷" },
    { value: "PN", label: "Pitcairn", flag: "🇵🇳" },
    { value: "TC", label: "Turks and Caicos Islands", flag: "🇹🇨" },
    { value: "VG", label: "British Virgin Islands", flag: "🇻🇬" },
    { value: "VI", label: "U.S. Virgin Islands", flag: "🇻🇮" },
    { value: "AI", label: "Anguilla", flag: "🇦🇮" },
    { value: "AW", label: "Aruba", flag: "🇦🇼" },
    { value: "CW", label: "Curaçao", flag: "🇨🇼" },
    { value: "SX", label: "Sint Maarten", flag: "🇸🇽" },
    { value: "BQ", label: "Caribbean Netherlands", flag: "🇧🇶" },
    { value: "BL", label: "Saint Barthélemy", flag: "🇧🇱" },
    { value: "MF", label: "Saint Martin", flag: "🇲🇫" },
    { value: "PM", label: "Saint Pierre and Miquelon", flag: "🇵🇲" },
    { value: "GL", label: "Greenland", flag: "🇬🇱" },
    { value: "FO", label: "Faroe Islands", flag: "🇫🇴" },
    { value: "AX", label: "Åland Islands", flag: "🇦🇽" },
    { value: "SJ", label: "Svalbard and Jan Mayen", flag: "🇸🇯" },
    { value: "GI", label: "Gibraltar", flag: "🇬🇮" },
    { value: "AD", label: "Andorra", flag: "🇦🇩" },
    { value: "MC", label: "Monaco", flag: "🇲🇨" },
    { value: "LI", label: "Liechtenstein", flag: "🇱🇮" },
    { value: "SM", label: "San Marino", flag: "🇸🇲" },
    { value: "VA", label: "Vatican City", flag: "🇻🇦" }
  ]

  // Check URL params for mode
  useEffect(() => {
    const mode = searchParams.get("mode")
    if (mode === "signup") {
      setActiveTab("signup")
    }
  }, [searchParams])

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      await login(formData.email, formData.password)
      // Redirect to dashboard
      window.location.href = "/dashboard"
    } catch (error) {
      console.error('Login failed:', error)
      // Error toast is already handled in auth context
    }
  }

  const handleSignup = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    setSuccess("")

    // Password validation
    if (formData.password.length < 8) {
      setError("Password must be at least 8 characters long")
      return
    }

    if (formData.password !== formData.confirmPassword) {
      setError("Passwords do not match")
      return
    }

    if (!formData.country) {
      setError("Please select your country")
      return
    }

    if (!formData.phone) {
      setError("Please enter your phone number")
      return
    }

    try {
      await signup({
        email: formData.email,
        password: formData.password,
        firstName: formData.firstName,
        lastName: formData.lastName,
        phone: formData.phone,
        country: formData.country,
        address: formData.address,
        referral_code: formData.referral_code
      })
      
      setSuccess("Account created successfully! Please check your email to verify your account.")
      
      // Redirect to login page after successful signup
      setTimeout(() => {
        setActiveTab("login")
        setSuccess("")
        // Clear form data
        setFormData({
          email: "",
          password: "",
          confirmPassword: "",
          firstName: "",
          lastName: "",
          phone: "",
          country: "",
          address: "",
          referral_code: ""
        })
      }, 2000) // Wait 2 seconds to show success message, then redirect
    } catch (error) {
      console.error('Signup failed:', error)
      setError("Failed to create account. Please try again.")
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-slate-950 dark:via-gray-900 dark:to-slate-950">
      <Navbar />
      
      <div className="container mx-auto px-4 py-24">
        <div className="max-w-md mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              {activeTab === "login" ? t("auth.welcomeBack") : t("auth.joinApex")}
            </h1>
            <p className="text-gray-600 dark:text-white/60">
              {activeTab === "login" ? t("auth.signInAccess") : t("auth.startJourney")}
            </p>
          </div>

          <Card className="shadow-xl border-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
            <CardHeader className="space-y-1">
              <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="login">Login</TabsTrigger>
                  <TabsTrigger value="signup">Sign Up</TabsTrigger>
                </TabsList>
                
                <TabsContent value="login" className="space-y-4 mt-6">
                  <form onSubmit={handleLogin} className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="email">Email</Label>
                      <div className="relative">
                        <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="email"
                          type="email"
                          placeholder="Enter your email"
                          className="pl-10"
                          value={formData.email}
                          onChange={(e) => handleInputChange('email', e.target.value)}
                          required
                        />
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="password">Password</Label>
                      <div className="relative">
                        <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="password"
                          type={showPassword ? "text" : "password"}
                          placeholder="Enter your password"
                          className="pl-10 pr-10"
                          value={formData.password}
                          onChange={(e) => handleInputChange('password', e.target.value)}
                          required
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? (
                            <EyeOff className="h-4 w-4 text-gray-400" />
                          ) : (
                            <Eye className="h-4 w-4 text-gray-400" />
                          )}
                        </Button>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Checkbox id="remember" />
                        <Label htmlFor="remember" className="text-sm">Remember me</Label>
                      </div>
                      <Link href="/forgot-password" className="text-sm text-blue-600 hover:underline">
                        Forgot password?
                      </Link>
                    </div>

                    <Button
                      type="submit"
                      className="w-full bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700"
                      disabled={isLoading}
                    >
                      {isLoading ? "Signing in..." : "Sign In"}
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </form>
                </TabsContent>

                <TabsContent value="signup" className="space-y-4 mt-6">
                  <form onSubmit={handleSignup} className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="firstName">First Name</Label>
                        <Input
                          id="firstName"
                          placeholder="John"
                          value={formData.firstName}
                          onChange={(e) => handleInputChange('firstName', e.target.value)}
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="lastName">Last Name</Label>
                        <Input
                          id="lastName"
                          placeholder="Doe"
                          value={formData.lastName}
                          onChange={(e) => handleInputChange('lastName', e.target.value)}
                          required
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="signupEmail">Email</Label>
                      <div className="relative">
                        <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="signupEmail"
                          type="email"
                          placeholder="Enter your email"
                          className="pl-10"
                          value={formData.email}
                          onChange={(e) => handleInputChange('email', e.target.value)}
                          required
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone Number</Label>
                      <PhoneInput
                        international
                        defaultCountry="US"
                        value={formData.phone}
                        onChange={(value) => handleInputChange('phone', value || '')}
                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="country">Country</Label>
                      <ReactSelect
                        options={countries}
                        value={countries.find(country => country.value === formData.country)}
                        onChange={(option) => handleInputChange('country', option?.value || '')}
                        placeholder="Select your country"
                        formatOptionLabel={(option) => (
                          <div className="flex items-center gap-2">
                            <span className="text-lg">{option.flag}</span>
                            <span>{option.label}</span>
                          </div>
                        )}
                        className="text-sm"
                        classNamePrefix="react-select"
                        styles={{
                          control: (base) => ({
                            ...base,
                            minHeight: '40px',
                            borderColor: 'hsl(var(--input))',
                            '&:hover': {
                              borderColor: 'hsl(var(--input))'
                            }
                          }),
                          option: (base, state) => ({
                            ...base,
                            backgroundColor: state.isSelected ? 'hsl(var(--primary))' : state.isFocused ? 'hsl(var(--accent))' : 'transparent',
                            color: state.isSelected ? 'hsl(var(--primary-foreground))' : 'hsl(var(--foreground))',
                            '&:hover': {
                              backgroundColor: 'hsl(var(--accent))'
                            }
                          })
                        }}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="address">Address</Label>
                      <Input
                        id="address"
                        placeholder="Enter your address"
                        value={formData.address}
                        onChange={(e) => handleInputChange('address', e.target.value)}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="referral_code">Referral Code (Optional)</Label>
                      <Input
                        id="referral_code"
                        placeholder="Enter referral code if you have one"
                        value={formData.referral_code}
                        onChange={(e) => handleInputChange('referral_code', e.target.value)}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="signupPassword">Password</Label>
                      <div className="relative">
                        <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="signupPassword"
                          type={showPassword ? "text" : "password"}
                          placeholder="Create a password (minimum 8 characters)"
                          className="pl-10 pr-10"
                          value={formData.password}
                          onChange={(e) => handleInputChange('password', e.target.value)}
                          minLength={8}
                          required
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? (
                            <EyeOff className="h-4 w-4 text-gray-400" />
                          ) : (
                            <Eye className="h-4 w-4 text-gray-400" />
                          )}
                        </Button>
                      </div>
                      {formData.password && formData.password.length < 8 && (
                        <p className="text-sm text-red-500">Password must be at least 8 characters long</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="confirmPassword">Confirm Password</Label>
                      <div className="relative">
                        <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="confirmPassword"
                          type={showConfirmPassword ? "text" : "password"}
                          placeholder="Confirm your password"
                          className="pl-10 pr-10"
                          value={formData.confirmPassword}
                          onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                          minLength={8}
                          required
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        >
                          {showConfirmPassword ? (
                            <EyeOff className="h-4 w-4 text-gray-400" />
                          ) : (
                            <Eye className="h-4 w-4 text-gray-400" />
                          )}
                        </Button>
                      </div>
                      {formData.confirmPassword && formData.password !== formData.confirmPassword && (
                        <p className="text-sm text-red-500">Passwords do not match</p>
                      )}
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox id="terms" required />
                      <Label htmlFor="terms" className="text-sm">
                        I agree to the{" "}
                        <Link href="/terms" className="text-blue-600 hover:underline">
                          Terms of Service
                        </Link>{" "}
                        and{" "}
                        <Link href="/privacy" className="text-blue-600 hover:underline">
                          Privacy Policy
                        </Link>
                      </Label>
                    </div>

                    <Button
                      type="submit"
                      className="w-full bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700"
                      disabled={isLoading}
                    >
                      {isLoading ? "Creating account..." : "Create Account"}
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </form>
                </TabsContent>
              </Tabs>
            </CardHeader>
          </Card>

          <div className="text-center mt-6">
            <p className="text-sm text-gray-600 dark:text-white/60">
              {activeTab === "login" ? "Don't have an account? " : "Already have an account? "}
              <Button
                variant="link"
                className="p-0 h-auto text-blue-600 hover:underline"
                onClick={() => setActiveTab(activeTab === "login" ? "signup" : "login")}
              >
                {activeTab === "login" ? "Sign up" : "Sign in"}
              </Button>
            </p>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  )
}
