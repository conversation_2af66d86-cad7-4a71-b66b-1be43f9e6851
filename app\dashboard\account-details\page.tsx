"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON>, T<PERSON>dingUp, Moon, Sun, DollarSign, Zap, Users, Award, Globe, User, LogIn, Flame, Gift, Newspaper } from "lucide-react"
import { useState, useEffect } from "react"
import Link from "next/link"
import LanguageSelector from "@/components/language-selector"
import { useLanguage } from "@/contexts/language-context"
import DashboardLayout from "@/components/dashboard/dashboard-layout"
import AccountDetails from "@/components/dashboard/account-details"

export default function DashboardAccountDetailsPage() {
  const [isDarkMode, setIsDarkMode] = useState(true)
  const { t, isRTL } = useLanguage()

  // Check system preference on initial load
  useEffect(() => {
    if (typeof window !== "undefined") {
      const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches
      setIsDarkMode(prefersDark)
    }
  }, [])

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode)
    document.documentElement.classList.toggle("dark")
  }

  return (
    <div
      className={`min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-slate-950 dark:via-gray-900 dark:to-slate-950 text-gray-900 dark:text-white overflow-hidden relative transition-colors duration-500 ${isRTL ? "rtl" : "ltr"}`}
    >
      {/* Custom CSS for enhanced UX */}
      <style jsx global>{`
        ::selection {
          background: ${isDarkMode ? "rgba(59, 130, 246, 0.3)" : "rgba(37, 99, 235, 0.2)"};
          color: ${isDarkMode ? "#ffffff" : "#1f2937"};
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
          width: 6px;
        }
        ::-webkit-scrollbar-track {
          background: ${isDarkMode ? "rgba(15, 23, 42, 0.1)" : "rgba(243, 244, 246, 0.5)"};
        }
        ::-webkit-scrollbar-thumb {
          background: ${isDarkMode ? "rgba(59, 130, 246, 0.3)" : "rgba(37, 99, 235, 0.3)"};
          border-radius: 3px;
        }
        ::-webkit-scrollbar-thumb:hover {
          background: ${isDarkMode ? "rgba(59, 130, 246, 0.5)" : "rgba(37, 99, 235, 0.5)"};
        }

        /* Smooth animations */
        * {
          transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
        }

        /* Subtle breathing animation */
        @keyframes subtle-breathe {
          0%, 100% { opacity: 0.4; transform: scale(1); }
          50% { opacity: 0.6; transform: scale(1.02); }
        }
        .subtle-breathe {
          animation: subtle-breathe 4s ease-in-out infinite;
        }

        /* Floating animation */
        @keyframes float {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-10px); }
        }
        .float {
          animation: float 3s ease-in-out infinite;
        }

        /* Gradient text animation */
        @keyframes gradient-shift {
          0%, 100% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
        }
        .gradient-text {
          background: linear-gradient(-45deg, #3b82f6, #8b5cf6, #06b6d4, #10b981);
          background-size: 400% 400%;
          animation: gradient-shift 3s ease infinite;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }
      `}</style>

      {/* Background Elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-500/10 dark:bg-blue-400/5 rounded-full blur-3xl" />
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/10 dark:bg-purple-400/5 rounded-full blur-3xl" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-cyan-500/5 dark:bg-cyan-400/3 rounded-full blur-3xl" />
      </div>

      {/* Main Content */}
      <main className="relative z-10">
        {/* Responsive Navigation */}
        <nav
          className="fixed top-4 left-4 right-4 z-40 flex items-center justify-between bg-transparent backdrop-blur-sm border border-gray-200/30 dark:border-white/10 rounded-2xl px-3 py-2 shadow-sm"
          role="navigation"
          aria-label="Main navigation"
        >
          {/* Logo */}
          <Link href="/" className="flex items-center gap-2 group">
            <div className="w-8 h-8 md:w-10 md:h-10 rounded-xl bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
              <TrendingUp className="w-4 h-4 md:w-5 md:h-5 text-white" />
            </div>
            <div className="hidden sm:block">
              <span className="text-lg md:text-xl font-bold bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-400 dark:to-cyan-400 bg-clip-text text-transparent">
                FxThrone
              </span>
            </div>
          </Link>

          {/* Navigation Links */}
          <div className="flex items-center gap-2 md:gap-4">
            <LanguageSelector />
            <Button
              variant="ghost"
              onClick={toggleTheme}
              className="text-xs md:text-sm font-light text-gray-700 dark:text-white/80 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/30 dark:hover:bg-white/5 transition-all duration-300 px-2 py-1 rounded-lg group"
              aria-label="Toggle between light and dark theme"
            >
              <div className="group-hover:rotate-180 transition-transform duration-500">
                {isDarkMode ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
              </div>
            </Button>
            <Link href="/trading-symbols">
              <Button
                variant="ghost"
                className="text-xs md:text-sm font-light text-gray-700 dark:text-white/80 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/30 dark:hover:bg-white/5 transition-all duration-300 px-2 py-1 rounded-lg"
              >
                <Globe className="h-3 w-3 mr-1" />
                Symbols
              </Button>
            </Link>
            <Link href="/economic-calendar">
              <Button
                variant="ghost"
                className="text-xs md:text-sm font-light text-gray-700 dark:text-white/80 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/30 dark:hover:bg-white/5 transition-all duration-300 px-2 py-1 rounded-lg"
              >
                <DollarSign className="h-3 w-3 mr-1" />
                Calendar
              </Button>
            </Link>
            <Link href="/affiliate">
              <Button
                variant="ghost"
                className="text-xs md:text-sm font-light text-gray-700 dark:text-white/80 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/30 dark:hover:bg-white/5 transition-all duration-300 px-2 py-1 rounded-lg"
              >
                <Users className="h-3 w-3 mr-1" />
                {t("nav.affiliate")}
              </Button>
            </Link>
            <Link href="/calculator">
              <Button
                variant="ghost"
                className="text-xs md:text-sm font-light text-gray-700 dark:text-white/80 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/30 dark:hover:bg-white/5 transition-all duration-300 px-2 py-1 rounded-lg"
              >
                <Zap className="h-3 w-3 mr-1" />
                {t("nav.calculator")}
              </Button>
            </Link>
            <Link href="/news">
              <Button
                variant="ghost"
                className="text-xs md:text-sm font-light text-gray-700 dark:text-white/80 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/30 dark:hover:bg-white/5 transition-all duration-300 px-2 py-1 rounded-lg"
              >
                <Newspaper className="h-3 w-3 mr-1" />
                News
              </Button>
            </Link>
            <Link href="/auth">
              <Button
                variant="ghost"
                className="text-xs md:text-sm font-light text-gray-700 dark:text-white/80 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/30 dark:hover:bg-white/5 transition-all duration-300 px-2 py-1 rounded-lg"
              >
                <LogIn className="h-3 w-3 mr-1" />
                {t("nav.login")}
              </Button>
            </Link>
            <Link href="/auth?mode=signup">
              <Button className="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white px-3 py-1 rounded-lg text-xs md:text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                <Flame className="h-3 w-3 mr-1" />
                {t("nav.getFunded")}
              </Button>
            </Link>
          </div>
        </nav>

        {/* Page Content with Dashboard Layout */}
        <div className="pt-20">
          <DashboardLayout activeTab="account-details" onTabChange={() => {}}>
            <AccountDetails />
          </DashboardLayout>
        </div>
      </main>
    </div>
  )
}