import { NextResponse } from 'next/server'

export async function GET() {
  try {
    // Option 1: Investing.com API (Economic calendar data)
    const INVESTING_API_KEY = process.env.INVESTING_API_KEY
    
    // Option 2: FXStreet API (Economic events)
    const FXSTREET_API_KEY = process.env.FXSTREET_API_KEY
    
    // Option 3: Bloomberg API (Professional economic data)
    const BLOOMBERG_API_KEY = process.env.BLOOMBERG_API_KEY
    
    // Option 4: Alpha Vantage Economic Calendar
    const ALPHA_VANTAGE_API_KEY = process.env.ALPHA_VANTAGE_API_KEY

    // Try to fetch from real APIs in order of preference
    let economicEvents = []

    // 1. Try Alpha Vantage Economic Calendar (Free tier available)
    if (ALPHA_VANTAGE_API_KEY && ALPHA_VANTAGE_API_KEY !== 'demo') {
      try {
        const today = new Date().toISOString().split('T')[0]
        const response = await fetch(
          `https://www.alphavantage.co/query?function=ECONOMIC_CALENDAR&time_from=${today}T0000&time_to=${today}T2359&apikey=${ALPHA_VANTAGE_API_KEY}`
        )
        
        if (response.ok) {
          const data = await response.json()
          if (data.economic_calendar) {
            economicEvents = data.economic_calendar.map((event: any) => ({
              time: new Date(event.time).toLocaleTimeString("en-US", {
                hour: "2-digit",
                minute: "2-digit",
                hour12: false,
              }),
              currency: event.currency,
              flag: getCurrencyFlag(event.currency),
              event: event.event,
              impact: getImpactLevel(event.importance),
              forecast: event.forecast || "-",
              previous: event.previous || "-",
              actual: event.actual || "-",
              status: event.actual ? "released" : "upcoming",
              source: "Alpha Vantage"
            }))
            
            if (economicEvents.length > 0) {
              return NextResponse.json({
                success: true,
                data: economicEvents,
                timestamp: new Date().toISOString(),
                source: "Alpha Vantage (Real Economic Data)"
              })
            }
          }
        }
      } catch (error) {
        console.error("Alpha Vantage Economic Calendar API error:", error)
      }
    }

    // 2. Try Investing.com API (Economic calendar)
    if (INVESTING_API_KEY) {
      try {
        const response = await fetch('https://api.investing.com/economic-calendar', {
          headers: {
            'Authorization': `Bearer ${INVESTING_API_KEY}`,
            'Content-Type': 'application/json'
          }
        })
        
        if (response.ok) {
          const data = await response.json()
          // Process Investing.com data
          economicEvents = data.events?.map((event: any) => ({
            time: new Date(event.time).toLocaleTimeString("en-US", {
              hour: "2-digit",
              minute: "2-digit",
              hour12: false,
            }),
            currency: event.currency,
            flag: getCurrencyFlag(event.currency),
            event: event.name,
            impact: getImpactLevel(event.importance),
            forecast: event.forecast || "-",
            previous: event.previous || "-",
            actual: event.actual || "-",
            status: event.actual ? "released" : "upcoming",
            source: "Investing.com"
          })) || []
          
          if (economicEvents.length > 0) {
            return NextResponse.json({
              success: true,
              data: economicEvents,
              timestamp: new Date().toISOString(),
              source: "Investing.com (Real Economic Data)"
            })
          }
        }
      } catch (error) {
        console.error("Investing.com API error:", error)
      }
    }

    // 3. Try FXStreet API (Economic events)
    if (FXSTREET_API_KEY) {
      try {
        const response = await fetch('https://api.fxstreet.com/economic-calendar', {
          headers: {
            'Authorization': `Bearer ${FXSTREET_API_KEY}`,
            'Content-Type': 'application/json'
          }
        })
        
        if (response.ok) {
          const data = await response.json()
          // Process FXStreet data
          economicEvents = data.events?.map((event: any) => ({
            time: new Date(event.time).toLocaleTimeString("en-US", {
              hour: "2-digit",
              minute: "2-digit",
              hour12: false,
            }),
            currency: event.currency,
            flag: getCurrencyFlag(event.currency),
            event: event.name,
            impact: getImpactLevel(event.importance),
            forecast: event.forecast || "-",
            previous: event.previous || "-",
            actual: event.actual || "-",
            status: event.actual ? "released" : "upcoming",
            source: "FXStreet"
          })) || []
          
          if (economicEvents.length > 0) {
            return NextResponse.json({
              success: true,
              data: economicEvents,
              timestamp: new Date().toISOString(),
              source: "FXStreet (Real Economic Data)"
            })
          }
        }
      } catch (error) {
        console.error("FXStreet API error:", error)
      }
    }

    // 4. Try Bloomberg API (Professional economic data)
    if (BLOOMBERG_API_KEY) {
      try {
        const response = await fetch('https://api.bloomberg.com/economic-calendar', {
          headers: {
            'Authorization': `Bearer ${BLOOMBERG_API_KEY}`,
            'Content-Type': 'application/json'
          }
        })
        
        if (response.ok) {
          const data = await response.json()
          // Process Bloomberg data
          economicEvents = data.events?.map((event: any) => ({
            time: new Date(event.time).toLocaleTimeString("en-US", {
              hour: "2-digit",
              minute: "2-digit",
              hour12: false,
            }),
            currency: event.currency,
            flag: getCurrencyFlag(event.currency),
            event: event.name,
            impact: getImpactLevel(event.importance),
            forecast: event.forecast || "-",
            previous: event.previous || "-",
            actual: event.actual || "-",
            status: event.actual ? "released" : "upcoming",
            source: "Bloomberg"
          })) || []
          
          if (economicEvents.length > 0) {
            return NextResponse.json({
              success: true,
              data: economicEvents,
              timestamp: new Date().toISOString(),
              source: "Bloomberg (Real Economic Data)"
            })
          }
        }
      } catch (error) {
        console.error("Bloomberg API error:", error)
      }
    }

    // Fallback to simulated data if no real APIs are available
    const simulatedEvents = [
      {
        time: "08:30",
        currency: "USD",
        flag: "🇺🇸",
        event: "Non-Farm Payrolls",
        impact: "high",
        forecast: "185K",
        previous: "199K",
        actual: "216K",
        status: "released",
      },
      {
        time: "10:00",
        currency: "EUR",
        flag: "🇪🇺",
        event: "ECB Interest Rate Decision",
        impact: "high",
        forecast: "4.50%",
        previous: "4.50%",
        actual: "-",
        status: "upcoming",
      },
      {
        time: "12:30",
        currency: "GBP",
        flag: "🇬🇧",
        event: "GDP Growth Rate",
        impact: "medium",
        forecast: "0.2%",
        previous: "0.1%",
        actual: "-",
        status: "upcoming",
      },
      {
        time: "14:00",
        currency: "CAD",
        flag: "🇨🇦",
        event: "Bank of Canada Rate Decision",
        impact: "high",
        forecast: "5.00%",
        previous: "5.00%",
        actual: "-",
        status: "upcoming",
      },
      {
        time: "15:30",
        currency: "AUD",
        flag: "🇦🇺",
        event: "Employment Change",
        impact: "medium",
        forecast: "15.0K",
        previous: "64.1K",
        actual: "-",
        status: "upcoming",
      },
      {
        time: "16:00",
        currency: "JPY",
        flag: "🇯🇵",
        event: "Core CPI",
        impact: "medium",
        forecast: "2.8%",
        previous: "2.8%",
        actual: "-",
        status: "upcoming",
      },
      {
        time: "09:00",
        currency: "EUR",
        flag: "🇪🇺",
        event: "German CPI",
        impact: "medium",
        forecast: "3.2%",
        previous: "3.1%",
        actual: "3.3%",
        status: "released",
      },
      {
        time: "11:00",
        currency: "GBP",
        flag: "🇬🇧",
        event: "Bank of England Rate Decision",
        impact: "high",
        forecast: "5.25%",
        previous: "5.25%",
        actual: "-",
        status: "upcoming",
      },
      {
        time: "13:00",
        currency: "USD",
        flag: "🇺🇸",
        event: "ISM Manufacturing PMI",
        impact: "medium",
        forecast: "49.0",
        previous: "48.7",
        actual: "-",
        status: "upcoming",
      },
      {
        time: "14:30",
        currency: "CAD",
        flag: "🇨🇦",
        event: "Trade Balance",
        impact: "low",
        forecast: "-1.2B",
        previous: "-1.1B",
        actual: "-",
        status: "upcoming",
      },
    ]

    // Add some randomization to make it feel more dynamic
    const randomizedEvents = simulatedEvents.map(event => {
      const now = new Date()
      const eventTime = new Date()
      const [hours, minutes] = event.time.split(':').map(Number)
      eventTime.setHours(hours, minutes, 0, 0)
      
      // If event time has passed, mark as released with random actual values
      if (eventTime < now && event.status === "upcoming") {
        const forecastNum = parseFloat(event.forecast.replace(/[^\d.-]/g, ''))
        const actualNum = forecastNum + (Math.random() - 0.5) * forecastNum * 0.1
        return {
          ...event,
          actual: actualNum.toFixed(1) + event.forecast.replace(/[\d.-]/g, ''),
          status: "released" as const,
          source: "Simulated Data"
        }
      }
      
      return {
        ...event,
        source: "Simulated Data"
      }
    })

    return NextResponse.json({
      success: true,
      data: randomizedEvents,
      timestamp: new Date().toISOString(),
      source: "Simulated Data (No API keys configured)"
    })
  } catch (error) {
    return NextResponse.json(
      { 
        success: false, 
        error: "Failed to fetch economic calendar data" 
      },
      { status: 500 }
    )
  }
}

function getCurrencyFlag(currency: string) {
  const currencyMap: { [key: string]: string } = {
    USD: "🇺🇸", EUR: "🇪🇺", GBP: "🇬🇧", CAD: "🇨🇦", AUD: "🇦🇺", JPY: "🇯🇵",
    CHF: "🇨🇭", NZD: "🇳🇿", SEK: "🇸🇪", NOK: "🇳🇴", DKK: "🇩🇰", PLN: "🇵🇱", CZK: "🇨🇿"
  }
  return currencyMap[currency] || "🌍"
}

function getImpactLevel(impact: string): "high" | "medium" | "low" {
  const impactLower = impact.toLowerCase()
  if (impactLower.includes("high") || impactLower.includes("3")) return "high"
  if (impactLower.includes("medium") || impactLower.includes("2")) return "medium"
  return "low"
} 