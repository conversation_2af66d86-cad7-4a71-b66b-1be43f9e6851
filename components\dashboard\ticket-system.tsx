"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Plus, MessageSquare, Clock, CheckCircle, AlertCircle, Send } from "lucide-react"
import { useState } from "react"
import { useLanguage } from "@/contexts/language-context"

export default function TicketSystem() {
  const { t } = useLanguage()
  const [showNewTicket, setShowNewTicket] = useState(false)
  const [selectedTicket, setSelectedTicket] = useState<number | null>(null)
  const [newMessage, setNewMessage] = useState("")

  const tickets = [
    {
      id: 1,
      subject: "Account verification issue",
      category: "account",
      priority: "high",
      status: "open",
      created: "2024-01-20",
      lastUpdated: "2024-01-21",
      responses: 3,
      messages: [
        {
          id: 1,
          sender: "user",
          message: "I'm having trouble verifying my account. The documents I uploaded seem to be rejected.",
          timestamp: "2024-01-20 10:30",
        },
        {
          id: 2,
          sender: "support",
          message:
            "Thank you for contacting us. We've reviewed your documents and found that the ID image is not clear enough. Please upload a higher quality image.",
          timestamp: "2024-01-20 14:15",
        },
        {
          id: 3,
          sender: "user",
          message: "I've uploaded a new ID image. Please check it now.",
          timestamp: "2024-01-21 09:20",
        },
      ],
    },
    {
      id: 2,
      subject: "Withdrawal processing time",
      category: "billing",
      priority: "medium",
      status: "in-progress",
      created: "2024-01-18",
      lastUpdated: "2024-01-19",
      responses: 2,
      messages: [
        {
          id: 1,
          sender: "user",
          message: "How long does it usually take for withdrawals to be processed?",
          timestamp: "2024-01-18 16:45",
        },
        {
          id: 2,
          sender: "support",
          message:
            "Withdrawals are typically processed within 24-48 hours during business days. Your withdrawal is currently being reviewed by our finance team.",
          timestamp: "2024-01-19 11:30",
        },
      ],
    },
    {
      id: 3,
      subject: "Trading platform connection",
      category: "technical",
      priority: "urgent",
      status: "resolved",
      created: "2024-01-15",
      lastUpdated: "2024-01-16",
      responses: 4,
      messages: [],
    },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "open":
        return "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400"
      case "in-progress":
        return "bg-cyan-100 dark:bg-cyan-900/30 text-cyan-700 dark:text-cyan-400"
      case "resolved":
        return "bg-indigo-100 dark:bg-indigo-900/30 text-indigo-700 dark:text-indigo-400"
      case "closed":
        return "bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-400"
      default:
        return "bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-400"
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "urgent":
        return "bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400"
      case "high":
        return "bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-400"
      case "medium":
        return "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400"
      case "low":
        return "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400"
      default:
        return "bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-400"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "open":
        return <AlertCircle className="w-4 h-4" />
      case "in-progress":
        return <Clock className="w-4 h-4" />
      case "resolved":
        return <CheckCircle className="w-4 h-4" />
      case "closed":
        return <CheckCircle className="w-4 h-4" />
      default:
        return <Clock className="w-4 h-4" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case "open":
        return t("ticket.open")
      case "in-progress":
        return t("ticket.inProgress")
      case "resolved":
        return t("ticket.resolved")
      case "closed":
        return t("ticket.closed")
      default:
        return status
    }
  }

  const getPriorityText = (priority: string) => {
    switch (priority) {
      case "urgent":
        return t("ticket.urgent")
      case "high":
        return t("ticket.high")
      case "medium":
        return t("ticket.medium")
      case "low":
        return t("ticket.low")
      default:
        return priority
    }
  }

  const getCategoryText = (category: string) => {
    switch (category) {
      case "general":
        return t("ticket.general")
      case "technical":
        return t("ticket.technical")
      case "billing":
        return t("ticket.billing")
      case "trading":
        return t("ticket.trading")
      case "account":
        return t("ticket.account")
      default:
        return category
    }
  }

  const handleSendMessage = () => {
    if (newMessage.trim() && selectedTicket) {
      // Add message logic here
      setNewMessage("")
    }
  }

  if (selectedTicket) {
    const ticket = tickets.find((t) => t.id === selectedTicket)
    if (!ticket) return null

    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <Button variant="ghost" onClick={() => setSelectedTicket(null)} className="mb-4">
              ← {t("common.back")}
            </Button>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              #{ticket.id} - {ticket.subject}
            </h1>
            <div className="flex items-center gap-4">
              <Badge className={getStatusColor(ticket.status)}>
                {getStatusIcon(ticket.status)}
                <span className="ml-1">{getStatusText(ticket.status)}</span>
              </Badge>
              <Badge className={getPriorityColor(ticket.priority)}>{getPriorityText(ticket.priority)}</Badge>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {t("ticket.created")}: {ticket.created}
              </span>
            </div>
          </div>
        </div>

        {/* Messages */}
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700">
          <div className="p-6 space-y-6">
            {ticket.messages.map((message) => (
              <div key={message.id} className={`flex ${message.sender === "user" ? "justify-end" : "justify-start"}`}>
                <div
                  className={`max-w-[70%] p-4 rounded-lg ${
                    message.sender === "user"
                      ? "bg-blue-500 text-white"
                      : "bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white"
                  }`}
                >
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-sm font-medium">
                      {message.sender === "user" ? t("ticket.you") : t("ticket.support")}
                    </span>
                    <span className="text-xs opacity-70">{message.timestamp}</span>
                  </div>
                  <p className="text-sm">{message.message}</p>
                </div>
              </div>
            ))}
          </div>

          {/* Message Input */}
          <div className="border-t border-gray-200 dark:border-gray-700 p-6">
            <div className="flex gap-4">
              <Textarea
                placeholder={t("ticket.typeMessage")}
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                className="flex-1"
                rows={3}
              />
              <Button
                onClick={handleSendMessage}
                disabled={!newMessage.trim()}
                className="self-end bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700"
              >
                <Send className="w-4 h-4 mr-2" />
                {t("ticket.send")}
              </Button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (showNewTicket) {
    return (
      <div className="space-y-6">
        {/* Header */}
        <div>
          <Button variant="ghost" onClick={() => setShowNewTicket(false)} className="mb-4">
            ← {t("common.back")}
          </Button>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">{t("ticket.new")}</h1>
          <p className="text-gray-600 dark:text-gray-400">{t("ticket.subtitle")}</p>
        </div>

        {/* New Ticket Form */}
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          <form className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="subject">{t("ticket.subject")}</Label>
                <Input id="subject" placeholder="Brief description of your issue" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="category">{t("ticket.category")}</Label>
                <select
                  id="category"
                  className="w-full px-3 py-2 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800"
                >
                  <option value="general">{t("ticket.general")}</option>
                  <option value="technical">{t("ticket.technical")}</option>
                  <option value="billing">{t("ticket.billing")}</option>
                  <option value="trading">{t("ticket.trading")}</option>
                  <option value="account">{t("ticket.account")}</option>
                </select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="priority">{t("ticket.priority")}</Label>
              <select
                id="priority"
                className="w-full px-3 py-2 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800"
              >
                <option value="low">{t("ticket.low")}</option>
                <option value="medium">{t("ticket.medium")}</option>
                <option value="high">{t("ticket.high")}</option>
                <option value="urgent">{t("ticket.urgent")}</option>
              </select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="message">{t("ticket.message")}</Label>
              <Textarea id="message" placeholder="Please describe your issue in detail..." rows={6} />
            </div>

            <div className="flex gap-4">
              <Button
                type="submit"
                className="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700"
              >
                {t("ticket.submit")}
              </Button>
              <Button type="button" variant="outline" onClick={() => setShowNewTicket(false)}>
                {t("common.cancel")}
              </Button>
            </div>
          </form>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">{t("ticket.title")}</h1>
          <p className="text-gray-600 dark:text-gray-400">{t("ticket.subtitle")}</p>
        </div>
        <Button
          onClick={() => setShowNewTicket(true)}
          className="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700"
        >
          <Plus className="w-4 h-4 mr-2" />
          {t("ticket.new")}
        </Button>
      </div>

      {/* Tickets List */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">{t("ticket.myTickets")}</h2>
        <div className="space-y-4">
          {tickets.map((ticket) => (
            <div
              key={ticket.id}
              className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
              onClick={() => setSelectedTicket(ticket.id)}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      #{ticket.id} - {ticket.subject}
                    </h3>
                    <Badge className={getStatusColor(ticket.status)}>
                      {getStatusIcon(ticket.status)}
                      <span className="ml-1">{getStatusText(ticket.status)}</span>
                    </Badge>
                    <Badge className={getPriorityColor(ticket.priority)}>{getPriorityText(ticket.priority)}</Badge>
                  </div>
                  <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                    <span>{getCategoryText(ticket.category)}</span>
                    <span>•</span>
                    <span>
                      {t("ticket.created")}: {ticket.created}
                    </span>
                    <span>•</span>
                    <span>
                      {t("ticket.lastUpdated")}: {ticket.lastUpdated}
                    </span>
                    <span>•</span>
                    <span>
                      {ticket.responses} {t("ticket.responses")}
                    </span>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <MessageSquare className="w-5 h-5 text-gray-400" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
