"use client"

import { Bad<PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import {
  TrendingUp,
  DollarSign,
  Target,
  Calendar,
  Award,
  Shield,
  Zap,
  ArrowRight,
  CheckCircle,
  Star,
  Clock,
  Users,
  Crown,
  Rocket,
  Gift,
  Trophy,
  Activity,
  ShoppingCart,
  MessageSquare,
  Share2,
  Heart,
  BookOpen,
  Video,
  Download,
  Upload,
  Plus,
  CheckCircle2,
  XCircle,
  AlertCircle,
} from "lucide-react"
import { useState } from "react"
import { useLanguage } from "@/contexts/language-context"

export default function EarnPoints() {
  const { t } = useLanguage()
  const [selectedCategory, setSelectedCategory] = useState("all")

  const pointStats = [
    {
      label: "Total Points Earned",
      value: "2,450",
      change: "+150",
      color: "text-green-600 dark:text-green-400",
      bgColor: "bg-green-100 dark:bg-green-900/30",
      icon: Trophy,
    },
    {
      label: "This Month",
      value: "380",
      change: "+45",
      color: "text-blue-600 dark:text-blue-400",
      bgColor: "bg-blue-100 dark:bg-blue-900/30",
      icon: TrendingUp,
    },
    {
      label: "Available to Redeem",
      value: "1,200",
      change: "Ready",
      color: "text-purple-600 dark:text-purple-400",
      bgColor: "bg-purple-100 dark:bg-purple-900/30",
      icon: Gift,
    },
    {
      label: "Level Progress",
      value: "75%",
      change: "Level 3",
      color: "text-orange-600 dark:text-orange-400",
      bgColor: "bg-orange-100 dark:bg-orange-900/30",
      icon: Crown,
    },
  ]

  const dailyTasks = [
    {
      id: 1,
      title: "Complete Daily Login",
      description: "Log in to your account",
      points: 10,
      completed: true,
      icon: CheckCircle,
      color: "text-green-600",
      bgColor: "bg-green-100 dark:bg-green-900/30",
    },
    {
      id: 2,
      title: "Make a Trade",
      description: "Execute any trade today",
      points: 25,
      completed: false,
      icon: TrendingUp,
      color: "text-blue-600",
      bgColor: "bg-blue-100 dark:bg-blue-900/30",
    },
    {
      id: 3,
      title: "Read Trading Guide",
      description: "Read one educational article",
      points: 15,
      completed: false,
      icon: BookOpen,
      color: "text-purple-600",
      bgColor: "bg-purple-100 dark:bg-purple-900/30",
    },
    {
      id: 4,
      title: "Watch Tutorial Video",
      description: "Watch a trading tutorial",
      points: 20,
      completed: false,
      icon: Video,
      color: "text-orange-600",
      bgColor: "bg-orange-100 dark:bg-orange-900/30",
    },
  ]

  const tradingActivities = [
    {
      id: 1,
      title: "First Trade of the Day",
      description: "Complete your first trade",
      points: 50,
      completed: true,
      icon: Zap,
      color: "text-yellow-600",
      bgColor: "bg-yellow-100 dark:bg-yellow-900/30",
    },
    {
      id: 2,
      title: "Profitable Trade",
      description: "Close a trade with profit",
      points: 100,
      completed: false,
      icon: DollarSign,
      color: "text-green-600",
      bgColor: "bg-green-100 dark:bg-green-900/30",
    },
    {
      id: 3,
      title: "Risk Management",
      description: "Use stop-loss in your trade",
      points: 30,
      completed: false,
      icon: Shield,
      color: "text-blue-600",
      bgColor: "bg-blue-100 dark:bg-blue-900/30",
    },
    {
      id: 4,
      title: "Multiple Trades",
      description: "Complete 5 trades today",
      points: 75,
      completed: false,
      icon: Activity,
      color: "text-purple-600",
      bgColor: "bg-purple-100 dark:bg-purple-900/30",
    },
  ]

  const socialActivities = [
    {
      id: 1,
      title: "Share on Social Media",
      description: "Share your trading success",
      points: 25,
      completed: false,
      icon: Share2,
      color: "text-blue-600",
      bgColor: "bg-blue-100 dark:bg-blue-900/30",
    },
    {
      id: 2,
      title: "Invite a Friend",
      description: "Refer a new trader",
      points: 200,
      completed: false,
      icon: Users,
      color: "text-green-600",
      bgColor: "bg-green-100 dark:bg-green-900/30",
    },
    {
      id: 3,
      title: "Leave a Review",
      description: "Review our platform",
      points: 50,
      completed: false,
      icon: MessageSquare,
      color: "text-orange-600",
      bgColor: "bg-orange-100 dark:bg-orange-900/30",
    },
    {
      id: 4,
      title: "Like Our Page",
      description: "Follow us on social media",
      points: 15,
      completed: false,
      icon: Heart,
      color: "text-red-600",
      bgColor: "bg-red-100 dark:bg-red-900/30",
    },
  ]

  const learningActivities = [
    {
      id: 1,
      title: "Complete Course Module",
      description: "Finish a trading course section",
      points: 150,
      completed: false,
      icon: BookOpen,
      color: "text-indigo-600",
      bgColor: "bg-indigo-100 dark:bg-indigo-900/30",
    },
    {
      id: 2,
      title: "Take Trading Quiz",
      description: "Complete a knowledge quiz",
      points: 75,
      completed: false,
      icon: Target,
      color: "text-purple-600",
      bgColor: "bg-purple-100 dark:bg-purple-900/30",
    },
    {
      id: 3,
      title: "Download Trading Tools",
      description: "Download our trading indicators",
      points: 30,
      completed: false,
      icon: Download,
      color: "text-blue-600",
      bgColor: "bg-blue-100 dark:bg-blue-900/30",
    },
    {
      id: 4,
      title: "Upload Trading Journal",
      description: "Share your trading journal",
      points: 100,
      completed: false,
      icon: Upload,
      color: "text-green-600",
      bgColor: "bg-green-100 dark:bg-green-900/30",
    },
  ]

  const categories = [
    { id: "all", name: "All Activities", count: dailyTasks.length + tradingActivities.length + socialActivities.length + learningActivities.length },
    { id: "daily", name: "Daily Tasks", count: dailyTasks.length },
    { id: "trading", name: "Trading Activities", count: tradingActivities.length },
    { id: "social", name: "Social Activities", count: socialActivities.length },
    { id: "learning", name: "Learning Activities", count: learningActivities.length },
  ]

  const getActivitiesByCategory = () => {
    switch (selectedCategory) {
      case "daily":
        return dailyTasks
      case "trading":
        return tradingActivities
      case "social":
        return socialActivities
      case "learning":
        return learningActivities
      default:
        return [...dailyTasks, ...tradingActivities, ...socialActivities, ...learningActivities]
    }
  }

  const activities = getActivitiesByCategory()

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Earn Points</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Complete activities to earn points and unlock exclusive rewards
          </p>
        </div>
        <Button className="gap-2 bg-blue-600 hover:bg-blue-700 text-white">
          <Gift className="h-4 w-4" />
          Redeem Points
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Stats Cards */}
          <div className="grid gap-4 md:grid-cols-2">
            {pointStats.map((stat, index) => (
              <Card key={index} className="border-2 border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-300">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        {stat.label}
                      </p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">{stat.value}</p>
                      <p className={`text-xs font-medium ${stat.color}`}>
                        {stat.change}
                      </p>
                    </div>
                    <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                      <stat.icon className={`h-6 w-6 ${stat.color}`} />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Level Progress */}
          <Card className="border-2 border-gray-200 dark:border-gray-700">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-gray-900 dark:text-white">
                <Crown className="h-5 w-5 text-yellow-600" />
                Level Progress
              </CardTitle>
              <CardDescription className="text-gray-600 dark:text-gray-400">
                You're 75% of the way to Level 4. Keep earning points to level up!
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
                  <span>Level 3</span>
                  <span>Level 4</span>
                </div>
                <Progress value={75} className="h-3" />
                <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                  <span>1,200 / 1,600 points</span>
                  <span>400 points needed</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Category Filter */}
          <Card className="border-2 border-gray-200 dark:border-gray-700">
            <CardHeader>
              <CardTitle className="text-gray-900 dark:text-white">Activity Categories</CardTitle>
              <CardDescription className="text-gray-600 dark:text-gray-400">
                Filter activities by category to focus on what interests you most
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {categories.map((category) => (
                  <Button
                    key={category.id}
                    variant={selectedCategory === category.id ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedCategory(category.id)}
                    className="gap-2 border-2 transition-all duration-300"
                  >
                    {category.name}
                    <Badge variant="secondary" className="ml-1">
                      {category.count}
                    </Badge>
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Activities Grid */}
          <Card className="border-2 border-gray-200 dark:border-gray-700">
            <CardHeader>
              <CardTitle className="text-gray-900 dark:text-white">Available Activities</CardTitle>
              <CardDescription className="text-gray-600 dark:text-gray-400">
                Complete these activities to earn points and advance your trading journey
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                {activities.map((activity) => (
                  <div
                    key={activity.id}
                    className={`relative p-6 rounded-xl border-2 transition-all duration-300 hover:shadow-lg ${
                      activity.completed
                        ? "border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/20"
                        : "border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600"
                    }`}
                  >
                    <div className="flex items-start justify-between mb-4">
                      <div className={`p-3 rounded-lg ${activity.bgColor}`}>
                        <activity.icon className={`h-5 w-5 ${activity.color}`} />
                      </div>
                      {activity.completed ? (
                        <CheckCircle2 className="h-5 w-5 text-green-600" />
                      ) : (
                        <XCircle className="h-5 w-5 text-gray-400" />
                      )}
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                      {activity.title}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400 mb-4">
                      {activity.description}
                    </p>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Star className="h-4 w-4 text-yellow-500" />
                        <span className="font-semibold text-gray-900 dark:text-white">{activity.points} points</span>
                      </div>
                      <Button
                        size="sm"
                        variant={activity.completed ? "outline" : "default"}
                        disabled={activity.completed}
                        className="gap-2"
                      >
                        {activity.completed ? "Completed" : "Complete"}
                        {!activity.completed && <ArrowRight className="h-3 w-3" />}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="lg:col-span-1">
          <div className="space-y-6">
            {/* Quick Stats */}
            <Card className="border-2 border-gray-200 dark:border-gray-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-gray-900 dark:text-white">
                  <Trophy className="h-5 w-5 text-yellow-600" />
                  Quick Stats
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div>
                    <div className="font-semibold text-gray-900 dark:text-white">Today's Progress</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">3 of 8 tasks completed</div>
                  </div>
                  <div className="text-right">
                    <div className="font-bold text-green-600">+85 points</div>
                    <div className="text-xs text-gray-500">This week</div>
                  </div>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div>
                    <div className="font-semibold text-gray-900 dark:text-white">Weekly Goal</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">500 points target</div>
                  </div>
                  <div className="text-right">
                    <div className="font-bold text-blue-600">75%</div>
                    <div className="text-xs text-gray-500">375/500</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Available Rewards */}
            <Card className="border-2 border-gray-200 dark:border-gray-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-gray-900 dark:text-white">
                  <Gift className="h-5 w-5 text-purple-600" />
                  Available Rewards
                </CardTitle>
                <CardDescription className="text-gray-600 dark:text-gray-400">
                  Redeem your points for exclusive rewards and bonuses
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {[
                  { name: "Trading Bonus", points: 500, value: "$50", available: true },
                  { name: "Premium Indicators", points: 300, value: "Free", available: true },
                  { name: "VIP Support", points: 1000, value: "1 Month", available: false },
                  { name: "Custom Strategy", points: 750, value: "Personal", available: true },
                  { name: "Account Upgrade", points: 1500, value: "Next Tier", available: false },
                  { name: "Cash Reward", points: 2000, value: "$200", available: false },
                ].map((reward, index) => (
                  <div
                    key={index}
                    className={`flex items-center justify-between p-3 rounded-lg border-2 transition-all duration-300 ${
                      reward.available
                        ? "border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/20"
                        : "border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800"
                    }`}
                  >
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-white">{reward.name}</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {reward.points} points
                      </p>
                    </div>
                    <div className="text-right">
                      <p className={`font-semibold ${reward.available ? "text-green-600" : "text-gray-400"}`}>
                        {reward.value}
                      </p>
                      <Button 
                        size="sm" 
                        variant={reward.available ? "default" : "outline"}
                        disabled={!reward.available}
                        className="mt-1"
                      >
                        {reward.available ? "Redeem" : "Locked"}
                      </Button>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Tips */}
            <Card className="border-2 border-gray-200 dark:border-gray-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-gray-900 dark:text-white">
                  <AlertCircle className="h-5 w-5 text-blue-600" />
                  Pro Tips
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-start gap-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <CheckCircle className="h-4 w-4 text-blue-600 mt-0.5" />
                  <div className="text-sm text-gray-700 dark:text-gray-300">
                    Complete daily tasks for consistent point accumulation
                  </div>
                </div>
                <div className="flex items-start gap-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <TrendingUp className="h-4 w-4 text-green-600 mt-0.5" />
                  <div className="text-sm text-gray-700 dark:text-gray-300">
                    Trading activities offer the highest point rewards
                  </div>
                </div>
                <div className="flex items-start gap-3 p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                  <Users className="h-4 w-4 text-purple-600 mt-0.5" />
                  <div className="text-sm text-gray-700 dark:text-gray-300">
                    Invite friends to earn bonus points and rewards
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
} 