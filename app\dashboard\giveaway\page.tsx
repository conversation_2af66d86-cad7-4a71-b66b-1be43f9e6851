"use client"

import { useState, useEffect } from "react"
import DashboardLayout from "@/components/dashboard/dashboard-layout"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { 
  Gift, 
  Clock, 
  Users, 
  DollarSign, 
  TrendingUp, 
  Award, 
  Star, 
  Zap, 
  Target,
  CheckCircle,
  AlertCircle,
  Play,
  Share2,
  Heart,
  Sparkles,
  User
} from "lucide-react"

interface Giveaway {
  id: string
  title: string
  description: string
  prize: string
  maxParticipants: number
  currentParticipants: number
  endDate: Date
  status: "active" | "upcoming" | "ended"
  requirements: string[]
  category: "daily" | "weekly" | "monthly" | "special"
  entryFee: number
  maxPrize: number
}

export default function DashboardGiveawayPage() {
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  })
  const [currentGiveaway, setCurrentGiveaway] = useState<Giveaway | null>(null)
  const [userEntries, setUserEntries] = useState(0)
  const [isParticipating, setIsParticipating] = useState(false)

  // Sample giveaway data
  const activeGiveaway: Giveaway = {
    id: "giveaway-001",
    title: "Mega Trading Challenge",
    description: "Join our biggest giveaway yet! Trade your way to a $100,000 funded account. Perfect for both beginners and experienced traders.",
    prize: "$100,000 Funded Account",
    maxParticipants: 1000,
    currentParticipants: 847,
    endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
    status: "active",
    requirements: [
      "Complete account verification",
      "Follow us on social media",
      "Share the giveaway",
      "Complete trading challenge",
      "Submit trading journal"
    ],
    category: "special",
    entryFee: 0,
    maxPrize: 100000
  }

  useEffect(() => {
    setCurrentGiveaway(activeGiveaway)
    
    const timer = setInterval(() => {
      const now = new Date().getTime()
      const distance = activeGiveaway.endDate.getTime() - now

      if (distance > 0) {
        const days = Math.floor(distance / (1000 * 60 * 60 * 24))
        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60))
        const seconds = Math.floor((distance % (1000 * 60)) / 1000)

        setTimeLeft({ days, hours, minutes, seconds })
      } else {
        setTimeLeft({ days: 0, hours: 0, minutes: 0, seconds: 0 })
      }
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  const handleParticipate = () => {
    setIsParticipating(true)
    setUserEntries(prev => prev + 1)
    // Simulate API call
    setTimeout(() => {
      setIsParticipating(false)
    }, 2000)
  }

  const getProgressPercentage = () => {
    if (!currentGiveaway) return 0
    return (currentGiveaway.currentParticipants / currentGiveaway.maxParticipants) * 100
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400"
      case "upcoming":
        return "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400"
      case "ended":
        return "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300"
      default:
        return "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300"
    }
  }

  return (
    <DashboardLayout activeTab="giveaway" onTabChange={() => {}}>
      <div className="space-y-8">
        {/* Header Section */}
        <div className="bg-gradient-to-r from-purple-500/10 to-pink-500/10 dark:from-purple-500/20 dark:to-pink-500/20 backdrop-blur-sm border border-purple-200 dark:border-purple-400/20 rounded-3xl p-8 shadow-xl">
          <div className="text-center">
            <Badge
              variant="outline"
              className="mb-6 text-sm font-light border-blue-300 dark:border-blue-400/30 text-blue-600 dark:text-blue-400 px-4 py-2 items-center"
            >
              <Gift className="w-4 h-4 mr-2" />
              Active Giveaway
            </Badge>
            
            <h1 className="text-4xl md:text-5xl font-bold mb-6 leading-tight">
              Win Up To{" "}
              <span className="bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-400 dark:to-cyan-400 bg-clip-text text-transparent">
                $100,000
              </span>
            </h1>
            
            <p className="text-lg md:text-xl text-gray-700 dark:text-white/70 mb-8 max-w-3xl mx-auto leading-relaxed">
              Join our exclusive trading giveaway and compete for a chance to win a fully funded trading account. 
              No entry fees, no hidden costs - just pure opportunity.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button
                size="lg"
                onClick={handleParticipate}
                disabled={isParticipating}
                className="bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-500 dark:to-cyan-500 text-white hover:from-blue-700 hover:to-cyan-700 dark:hover:from-blue-600 dark:hover:to-cyan-600 text-lg px-8 py-4 h-auto"
              >
                {isParticipating ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2" />
                    Joining...
                  </>
                ) : (
                  <>
                    <Gift className="w-5 h-5 mr-2" />
                    Join Giveaway Now
                  </>
                )}
              </Button>
              
              <Button
                variant="outline"
                size="lg"
                className="border-2 border-gray-300 dark:border-white/20 text-gray-700 dark:text-white/70 hover:bg-gray-50 dark:hover:bg-white/5 text-lg px-8 py-4 h-auto"
              >
                <Share2 className="w-5 h-5 mr-2" />
                Share & Earn Bonus
              </Button>
            </div>
          </div>
        </div>

        {/* Active Giveaway Section */}
        {currentGiveaway && (
          <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-8 shadow-xl">
            {/* Giveaway Header */}
            <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between mb-8">
              <div className="flex items-center gap-4 mb-6 lg:mb-0">
                <div className="w-16 h-16 rounded-2xl bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center shadow-lg">
                  <Gift className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                    {currentGiveaway.title}
                  </h2>
                  <div className="flex items-center gap-4">
                    <Badge className={getStatusColor(currentGiveaway.status)}>
                      {currentGiveaway.status === "active" ? "Active" : "Upcoming"}
                    </Badge>
                    <Badge variant="outline" className="border-blue-300 dark:border-blue-400/30 text-blue-600 dark:text-blue-400">
                      {currentGiveaway.category.charAt(0).toUpperCase() + currentGiveaway.category.slice(1)}
                    </Badge>
                  </div>
                </div>
              </div>
              
              <div className="text-right">
                <div className="text-4xl font-bold text-blue-600 dark:text-blue-400 mb-1">
                  ${currentGiveaway.maxPrize.toLocaleString()}
                </div>
                <div className="text-sm text-gray-600 dark:text-white/60">Maximum Prize</div>
              </div>
            </div>

            {/* Countdown Timer */}
            <div className="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20 rounded-2xl p-6 mb-8">
              <div className="text-center mb-6">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">Time Remaining</h3>
                <p className="text-gray-600 dark:text-white/60">Hurry up! This giveaway ends soon</p>
              </div>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {[
                  { label: "Days", value: timeLeft.days },
                  { label: "Hours", value: timeLeft.hours },
                  { label: "Minutes", value: timeLeft.minutes },
                  { label: "Seconds", value: timeLeft.seconds }
                ].map((item, index) => (
                  <div key={index} className="text-center">
                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-lg">
                      <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-1">
                        {item.value.toString().padStart(2, '0')}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-white/60">{item.label}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Progress and Stats */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Participation Progress</h3>
                  <span className="text-sm text-gray-600 dark:text-white/60">
                    {currentGiveaway.currentParticipants} / {currentGiveaway.maxParticipants}
                  </span>
                </div>
                <Progress value={getProgressPercentage()} className="h-3 mb-4" />
                <p className="text-sm text-gray-600 dark:text-white/60">
                  {currentGiveaway.maxParticipants - currentGiveaway.currentParticipants} spots remaining
                </p>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-gray-50 dark:bg-gray-800 rounded-xl p-4 text-center">
                  <Users className="w-8 h-8 text-blue-600 dark:text-blue-400 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900 dark:text-white">
                    {currentGiveaway.currentParticipants}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-white/60">Participants</div>
                </div>
                
                <div className="bg-gray-50 dark:bg-gray-800 rounded-xl p-4 text-center">
                  <DollarSign className="w-8 h-8 text-green-600 dark:text-green-400 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900 dark:text-white">
                    ${currentGiveaway.maxPrize.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-white/60">Total Prize Pool</div>
                </div>
              </div>
            </div>

            {/* Requirements */}
            <div className="mb-8">
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Entry Requirements</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {currentGiveaway.requirements.map((requirement, index) => (
                  <div key={index} className="flex items-center gap-3 p-4 bg-gray-50 dark:bg-gray-800 rounded-xl">
                    <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400 flex-shrink-0" />
                    <span className="text-gray-700 dark:text-white/80">{requirement}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                onClick={handleParticipate}
                disabled={isParticipating}
                className="flex-1 bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-500 dark:to-cyan-500 text-white hover:from-blue-700 hover:to-cyan-700 dark:hover:from-blue-600 dark:hover:to-cyan-600 h-12 text-lg"
              >
                {isParticipating ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Joining...
                  </>
                ) : (
                  <>
                    <Gift className="w-5 h-5 mr-2" />
                    Join Now - Free Entry
                  </>
                )}
              </Button>
              
              <Button
                variant="outline"
                className="border-2 border-gray-300 dark:border-white/20 text-gray-700 dark:text-white/70 hover:bg-gray-50 dark:hover:bg-white/5 h-12"
              >
                <Share2 className="w-5 h-5 mr-2" />
                Share
              </Button>
            </div>
          </div>
        )}

        {/* Prize Tiers Section */}
        <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-8 shadow-xl">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
              Prize{" "}
              <span className="bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-400 dark:to-cyan-400 bg-clip-text text-transparent">
                Tiers
              </span>
            </h2>
            <p className="text-lg text-gray-700 dark:text-white/70 max-w-3xl mx-auto">
              Multiple winners, multiple opportunities. Our giveaways feature various prize tiers to maximize your chances of winning.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                tier: "1st Place",
                prize: "$100,000",
                description: "Fully funded trading account with maximum leverage",
                color: "from-yellow-500 to-orange-500",
                participants: "1 Winner"
              },
              {
                tier: "2nd Place",
                prize: "$50,000",
                description: "Funded account with premium trading features",
                color: "from-gray-400 to-gray-600",
                participants: "2 Winners"
              },
              {
                tier: "3rd Place",
                prize: "$25,000",
                description: "Funded account with standard trading features",
                color: "from-amber-600 to-yellow-600",
                participants: "5 Winners"
              }
            ].map((tier, index) => (
              <Card key={index} className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
                <CardHeader className="text-center">
                  <div className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${tier.color} flex items-center justify-center mx-auto mb-4`}>
                    <Award className="w-8 h-8 text-white" />
                  </div>
                  <CardTitle className="text-2xl text-gray-900 dark:text-white">{tier.tier}</CardTitle>
                  <div className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-400 dark:to-cyan-400 bg-clip-text text-transparent">
                    {tier.prize}
                  </div>
                  <Badge variant="outline" className="border-blue-300 dark:border-blue-400/30 text-blue-600 dark:text-blue-400">
                    {tier.participants}
                  </Badge>
                </CardHeader>
                <CardContent className="text-center">
                  <CardDescription className="text-gray-700 dark:text-white/70 text-base">
                    {tier.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Features Section */}
        <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-8 shadow-xl">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
              Why Join Our{" "}
              <span className="bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-400 dark:to-cyan-400 bg-clip-text text-transparent">
                Giveaways?
              </span>
            </h2>
            <p className="text-lg text-gray-700 dark:text-white/70 max-w-3xl mx-auto">
              We believe in giving back to our trading community. Our giveaways are designed to help traders 
              achieve their financial goals with real funded accounts.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: <Zap className="w-8 h-8" />,
                title: "Zero Entry Fees",
                description: "All our giveaways are completely free to enter. No hidden costs or fees.",
                color: "text-blue-600 dark:text-blue-400"
              },
              {
                icon: <Target className="w-8 h-8" />,
                title: "Real Funded Accounts",
                description: "Winners receive actual funded trading accounts, not demo accounts.",
                color: "text-green-600 dark:text-green-400"
              },
              {
                icon: <Award className="w-8 h-8" />,
                title: "Multiple Winners",
                description: "We have multiple winners in each giveaway, increasing your chances.",
                color: "text-purple-600 dark:text-purple-400"
              },
              {
                icon: <Users className="w-8 h-8" />,
                title: "Community Driven",
                description: "Join our growing community of successful traders and mentors.",
                color: "text-orange-600 dark:text-orange-400"
              },
              {
                icon: <TrendingUp className="w-8 h-8" />,
                title: "Trading Education",
                description: "Access to premium trading courses and mentorship programs.",
                color: "text-cyan-600 dark:text-cyan-400"
              },
              {
                icon: <Star className="w-8 h-8" />,
                title: "Exclusive Benefits",
                description: "Special perks and bonuses for giveaway participants and winners.",
                color: "text-pink-600 dark:text-pink-400"
              }
            ].map((feature, index) => (
              <Card key={index} className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
                <CardHeader>
                  <div className={`w-12 h-12 rounded-xl bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center mb-4 ${feature.color}`}>
                    {feature.icon}
                  </div>
                  <CardTitle className="text-xl text-gray-900 dark:text-white">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-700 dark:text-white/70 text-base">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* How to Participate Section */}
        <div className="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20 backdrop-blur-sm border border-blue-200 dark:border-blue-400/20 rounded-3xl p-8 shadow-xl">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
              How to{" "}
              <span className="bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-400 dark:to-cyan-400 bg-clip-text text-transparent">
                Participate
              </span>
            </h2>
            <p className="text-lg text-gray-700 dark:text-white/70 max-w-3xl mx-auto">
              Follow these simple steps to enter our giveaway and increase your chances of winning.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                step: "01",
                title: "Create Account",
                description: "Sign up for a free trading account on our platform",
                icon: <User className="w-6 h-6" />
              },
              {
                step: "02",
                title: "Complete Verification",
                description: "Verify your identity and complete KYC process",
                icon: <CheckCircle className="w-6 h-6" />
              },
              {
                step: "03",
                title: "Follow & Share",
                description: "Follow us on social media and share the giveaway",
                icon: <Share2 className="w-6 h-6" />
              },
              {
                step: "04",
                title: "Wait for Results",
                description: "Winners will be announced on the end date",
                icon: <Award className="w-6 h-6" />
              }
            ].map((step, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 rounded-2xl bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center mx-auto mb-4 shadow-lg">
                  <div className="text-white font-bold text-lg">{step.step}</div>
                </div>
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">{step.title}</h3>
                <p className="text-gray-700 dark:text-white/70">{step.description}</p>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <Button
              size="lg"
              onClick={handleParticipate}
              disabled={isParticipating}
              className="bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-500 dark:to-cyan-500 text-white hover:from-blue-700 hover:to-cyan-700 dark:hover:from-blue-600 dark:hover:to-cyan-600 text-lg px-8 py-4 h-auto"
            >
              {isParticipating ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2" />
                  Joining...
                </>
              ) : (
                <>
                  <Gift className="w-5 h-5 mr-2" />
                  Start Your Journey
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Footer CTA */}
        <div className="bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-500 dark:to-cyan-500 rounded-3xl p-12 text-center text-white shadow-2xl">
          <Sparkles className="w-16 h-16 mx-auto mb-6" />
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Win $100,000?
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Don't miss this opportunity to transform your trading career. Join thousands of traders 
            competing for life-changing prizes.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="lg"
              onClick={handleParticipate}
              disabled={isParticipating}
              className="bg-white text-blue-600 hover:bg-gray-100 text-lg px-8 py-4 h-auto"
            >
              {isParticipating ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-2" />
                  Joining...
                </>
              ) : (
                <>
                  <Gift className="w-5 h-5 mr-2" />
                  Join Giveaway Now
                </>
              )}
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="border-white text-white hover:bg-white/10 text-lg px-8 py-4 h-auto"
            >
              <Share2 className="w-5 h-5 mr-2" />
              Share with Friends
            </Button>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
} 