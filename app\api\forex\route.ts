import { NextResponse } from 'next/server'

export async function GET() {
  try {
    // TradingView API integration
    const TRADINGVIEW_API_KEY = process.env.TRADINGVIEW_API_KEY
    
    // Alternative: TradingView WebSocket for real-time data
    const TRADINGVIEW_WEBSOCKET_URL = process.env.TRADINGVIEW_WEBSOCKET_URL
    
    // TradingView Charting Library (if available)
    const TRADINGVIEW_CHARTING_LIBRARY = process.env.TRADINGVIEW_CHARTING_LIBRARY

    const symbols = [
      "EURUSD", "GBPUSD", "USDJPY", "USDCAD", "AUDUSD", "USDCHF",
      "EURGBP", "EURJPY", "GBPJPY", "EURCHF", "GBPCHF", "AUDCAD",
      "NZDUSD", "USDSEK", "USDNOK", "USDDKK", "USDPLN", "USDCZK"
    ]

    // Try to fetch from TradingView APIs
    let forexData = []

    // 1. Try TradingView API (if available)
    if (TRADINGVIEW_API_KEY) {
      try {
        // TradingView API endpoint for forex data
        const response = await fetch('https://api.tradingview.com/v1/forex/quotes', {
          headers: {
            'Authorization': `Bearer ${TRADINGVIEW_API_KEY}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            symbols: symbols.map(s => `${s.slice(0, 3)}${s.slice(3, 6)}`)
          })
        })
        
        if (response.ok) {
          const data = await response.json()
          forexData = data.quotes?.map((quote: any) => ({
            symbol: quote.symbol,
            name: `${quote.symbol.slice(0, 3)} vs ${quote.symbol.slice(3, 6)}`,
            category: getCategory(quote.symbol),
            price: quote.price?.toFixed(4) || "0.0000",
            change: quote.change?.toFixed(4) || "0.0000",
            changePercent: `${quote.changePercent?.toFixed(2) || "0.00"}%`,
            trend: (quote.changePercent || 0) > 0 ? "up" : "down",
            spread: quote.spread?.toFixed(1) || "0.8",
            volume: getVolumeLevel(quote.volume),
            flag1: getCurrencyFlag(quote.symbol.slice(0, 3)),
            flag2: getCurrencyFlag(quote.symbol.slice(3, 6)),
            source: "TradingView"
          })) || []
          
          if (forexData.length > 0) {
            return NextResponse.json({
              success: true,
              data: forexData,
              timestamp: new Date().toISOString(),
              source: "TradingView (Real Data)"
            })
          }
        }
      } catch (error) {
        console.error("TradingView API error:", error)
      }
    }

    // 2. Try TradingView WebSocket for real-time data
    if (TRADINGVIEW_WEBSOCKET_URL) {
      try {
        // Real WebSocket connection for real-time data
        const response = await fetch(TRADINGVIEW_WEBSOCKET_URL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${TRADINGVIEW_API_KEY}`
          },
          body: JSON.stringify({
            action: 'subscribe',
            symbols: symbols
          })
        })
        
        if (response.ok) {
          const data = await response.json()
          forexData = data.quotes?.map((quote: any) => ({
            symbol: quote.symbol,
            name: `${quote.symbol.slice(0, 3)} vs ${quote.symbol.slice(3, 6)}`,
            category: getCategory(quote.symbol),
            price: quote.price?.toFixed(4) || "0.0000",
            change: quote.change?.toFixed(4) || "0.0000",
            changePercent: `${quote.changePercent?.toFixed(2) || "0.00"}%`,
            trend: (quote.changePercent || 0) > 0 ? "up" : "down",
            spread: quote.spread?.toFixed(1) || "0.8",
            volume: getVolumeLevel(quote.volume),
            flag1: getCurrencyFlag(quote.symbol.slice(0, 3)),
            flag2: getCurrencyFlag(quote.symbol.slice(3, 6)),
            source: "TradingView WebSocket"
          })) || []
          
          return NextResponse.json({
            success: true,
            data: forexData,
            timestamp: new Date().toISOString(),
            source: "TradingView WebSocket (Real Data)"
          })
        }
      } catch (error) {
        console.error("TradingView WebSocket error:", error)
      }
    }

    // 3. Try TradingView Charting Library integration
    if (TRADINGVIEW_CHARTING_LIBRARY) {
      try {
        // TradingView Charting Library provides real-time data
        const response = await fetch('https://api.tradingview.com/v1/charting/quotes', {
          headers: {
            'Authorization': `Bearer ${TRADINGVIEW_CHARTING_LIBRARY}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            symbols: symbols
          })
        })
        
        if (response.ok) {
          const data = await response.json()
          forexData = data.quotes?.map((quote: any) => ({
            symbol: quote.symbol,
            name: `${quote.symbol.slice(0, 3)} vs ${quote.symbol.slice(3, 6)}`,
            category: getCategory(quote.symbol),
            price: quote.price?.toFixed(4) || "0.0000",
            change: quote.change?.toFixed(4) || "0.0000",
            changePercent: `${quote.changePercent?.toFixed(2) || "0.00"}%`,
            trend: (quote.changePercent || 0) > 0 ? "up" : "down",
            spread: quote.spread?.toFixed(1) || "0.8",
            volume: getVolumeLevel(quote.volume),
            flag1: getCurrencyFlag(quote.symbol.slice(0, 3)),
            flag2: getCurrencyFlag(quote.symbol.slice(3, 6)),
            source: "TradingView Charting Library"
          })) || []
          
          return NextResponse.json({
            success: true,
            data: forexData,
            timestamp: new Date().toISOString(),
            source: "TradingView Charting Library (Real Data)"
          })
        }
      } catch (error) {
        console.error("TradingView Charting Library error:", error)
      }
    }

    // 4. Try Alpha Vantage as fallback (Free tier available)
    const ALPHA_VANTAGE_API_KEY = process.env.ALPHA_VANTAGE_API_KEY || 'demo'
    if (ALPHA_VANTAGE_API_KEY && ALPHA_VANTAGE_API_KEY !== 'demo') {
      try {
        const promises = symbols.slice(0, 5).map(async (symbol) => {
          const response = await fetch(
            `https://www.alphavantage.co/query?function=CURRENCY_EXCHANGE_RATE&from_currency=${symbol.slice(0, 3)}&to_currency=${symbol.slice(3, 6)}&apikey=${ALPHA_VANTAGE_API_KEY}`
          )
          
          if (response.ok) {
            const data = await response.json()
            if (data["Realtime Currency Exchange Rate"]) {
              const rate = data["Realtime Currency Exchange Rate"]
              return {
                symbol,
                name: `${rate["2. From_Currency Name"]} vs ${rate["4. To_Currency Name"]}`,
                category: getCategory(symbol),
                price: rate["5. Exchange Rate"],
                change: "0.0000",
                changePercent: "0.00%",
                trend: "up",
                spread: "0.8",
                volume: "High",
                flag1: getCurrencyFlag(symbol.slice(0, 3)),
                flag2: getCurrencyFlag(symbol.slice(3, 6)),
                source: "Alpha Vantage"
              }
            }
          }
          return null
        })

        const results = await Promise.all(promises)
        forexData = results.filter(result => result !== null)
        
        if (forexData.length > 0) {
          return NextResponse.json({
            success: true,
            data: forexData,
            timestamp: new Date().toISOString(),
            source: "Alpha Vantage (Real Data)"
          })
        }
      } catch (error) {
        console.error("Alpha Vantage API error:", error)
      }
    }

    // No real data available - return error
    return NextResponse.json(
      { 
        success: false, 
        error: "No real data sources available. Please configure TradingView API keys or Alpha Vantage API key.",
        message: "To get real data, add your API keys to .env.local file"
      },
      { status: 503 }
    )

  } catch (error) {
    return NextResponse.json(
      { 
        success: false, 
        error: "Failed to fetch real forex data" 
      },
      { status: 500 }
    )
  }
}

function getCategory(symbol: string): "major" | "cross" | "minor" {
  const majorPairs = ["EURUSD", "GBPUSD", "USDJPY", "USDCAD", "AUDUSD", "USDCHF"]
  const crossPairs = ["EURGBP", "EURJPY", "GBPJPY", "EURCHF", "GBPCHF", "AUDCAD"]
  
  if (majorPairs.includes(symbol)) return "major"
  if (crossPairs.includes(symbol)) return "cross"
  return "minor"
}

function getCurrencyFlag(currency: string) {
  const currencyMap: { [key: string]: string } = {
    USD: "🇺🇸", EUR: "🇪🇺", GBP: "🇬🇧", CAD: "🇨🇦", AUD: "🇦🇺", JPY: "🇯🇵",
    CHF: "🇨🇭", NZD: "🇳🇿", SEK: "🇸🇪", NOK: "🇳🇴", DKK: "🇩🇰", PLN: "🇵🇱", CZK: "🇨🇿"
  }
  return currencyMap[currency] || "🌍"
}

function getVolumeLevel(volume?: number): "High" | "Medium" | "Low" {
  if (volume) {
    if (volume > 1000000) return "High"
    if (volume > 500000) return "Medium"
    return "Low"
  }
  return "Medium"
} 