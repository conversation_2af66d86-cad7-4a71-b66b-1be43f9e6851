"use client"

import { useState, useEffect, Suspense } from "react"
import { useSearchParams, useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { CheckCircle, XCircle, Mail, ArrowRight, Loader2 } from "lucide-react"
import { toast } from "sonner"
import Navbar from "@/components/navbar"
import Footer from "@/components/footer"

function VerifyEmailContent() {
  const [isVerifying, setIsVerifying] = useState(false)
  const [isVerified, setIsVerified] = useState(false)
  const [verificationError, setVerificationError] = useState<string | null>(null)
  const [email, setEmail] = useState("")
  const [code, setCode] = useState("")
  const [manualEmail, setManualEmail] = useState("")
  const [manualCode, setManualCode] = useState("")
  const [showManualForm, setShowManualForm] = useState(false)
  
  const searchParams = useSearchParams()
  const router = useRouter()

  useEffect(() => {
    // Get email and code from URL parameters
    const emailParam = searchParams.get('email')
    const codeParam = searchParams.get('code')
    
    if (emailParam && codeParam) {
      setEmail(emailParam)
      setCode(codeParam)
      // Auto-verify if both parameters are present
      verifyEmail(emailParam, codeParam)
    }
  }, [searchParams])

  const verifyEmail = async (emailToVerify: string, codeToVerify: string) => {
    setIsVerifying(true)
    setVerificationError(null)
    
    try {
      const response = await fetch(`https://xthronebackend-9dbecc0e3719.herokuapp.com/auth/verify-email?email=${encodeURIComponent(emailToVerify)}&code=${encodeURIComponent(codeToVerify)}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (response.ok) {
        setIsVerified(true)
        toast.success("Email verified successfully! 🎉", {
          description: "Your email has been verified. You can now log in to your account.",
          duration: 5000,
        })
        
        // Redirect to login after 3 seconds
        setTimeout(() => {
          router.push('/auth')
        }, 3000)
      } else {
        const errorData = await response.text()
        setVerificationError(`Verification failed: ${response.status} - ${errorData}`)
        toast.error("Email verification failed", {
          description: "Please check your verification code and try again.",
          duration: 5000,
        })
      }
    } catch (error) {
      console.error('Email verification error:', error)
      setVerificationError("Network error. Please check your connection and try again.")
      toast.error("Verification failed", {
        description: "Network error. Please try again.",
        duration: 5000,
      })
    } finally {
      setIsVerifying(false)
    }
  }

  const handleManualVerification = (e: React.FormEvent) => {
    e.preventDefault()
    if (!manualEmail || !manualCode) {
      toast.error("Please enter both email and verification code")
      return
    }
    verifyEmail(manualEmail, manualCode)
  }

  const handleResendCode = async () => {
    if (!email) {
      toast.error("Please enter your email address")
      return
    }
    
    try {
      const response = await fetch('https://xthronebackend-9dbecc0e3719.herokuapp.com/auth/resend-verification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      })

      if (response.ok) {
        toast.success("Verification code sent!", {
          description: "Please check your email for the new verification code.",
          duration: 5000,
        })
      } else {
        toast.error("Failed to resend code", {
          description: "Please try again later.",
          duration: 5000,
        })
      }
    } catch (error) {
      toast.error("Network error", {
        description: "Please check your connection and try again.",
        duration: 5000,
      })
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-slate-950 dark:via-gray-900 dark:to-slate-950">
      <Navbar />
      
      <div className="container mx-auto px-4 py-24">
        <div className="max-w-md mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              Verify Your Email
            </h1>
            <p className="text-gray-600 dark:text-white/60">
              Please verify your email address to complete your registration
            </p>
          </div>

          <Card className="shadow-xl border-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
            <CardHeader className="space-y-1">
              <CardTitle className="text-2xl font-bold text-center">
                Email Verification
              </CardTitle>
              <CardDescription className="text-center">
                {isVerified 
                  ? "Your email has been verified successfully!"
                  : "Enter your email and verification code to verify your account"
                }
              </CardDescription>
            </CardHeader>
            
            <CardContent className="space-y-6">
              {isVerified ? (
                <div className="text-center space-y-4">
                  <div className="flex justify-center">
                    <CheckCircle className="h-16 w-16 text-green-500" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-green-600 dark:text-green-400">
                      Verification Successful!
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400 mt-2">
                      Redirecting to login page...
                    </p>
                  </div>
                  <Button 
                    onClick={() => router.push('/auth')}
                    className="w-full"
                  >
                    Go to Login
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              ) : verificationError ? (
                <div className="text-center space-y-4">
                  <div className="flex justify-center">
                    <XCircle className="h-16 w-16 text-red-500" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-red-600 dark:text-red-400">
                      Verification Failed
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400 mt-2">
                      {verificationError}
                    </p>
                  </div>
                  <div className="space-y-3">
                    <Button 
                      onClick={() => setShowManualForm(true)}
                      variant="outline"
                      className="w-full"
                    >
                      Try Manual Verification
                    </Button>
                    <Button 
                      onClick={handleResendCode}
                      variant="ghost"
                      className="w-full"
                    >
                      Resend Verification Code
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  {!showManualForm ? (
                    <div className="text-center space-y-4">
                      <div className="flex justify-center">
                        <Mail className="h-16 w-16 text-blue-500" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold">
                          Check Your Email
                        </h3>
                        <p className="text-gray-600 dark:text-gray-400 mt-2">
                          We've sent a verification code to your email address.
                        </p>
                      </div>
                      {isVerifying ? (
                        <div className="flex items-center justify-center space-x-2">
                          <Loader2 className="h-5 w-5 animate-spin" />
                          <span>Verifying...</span>
                        </div>
                      ) : (
                        <Button 
                          onClick={() => setShowManualForm(true)}
                          className="w-full"
                        >
                          Enter Code Manually
                        </Button>
                      )}
                    </div>
                  ) : (
                    <form onSubmit={handleManualVerification} className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="email">Email Address</Label>
                        <Input
                          id="email"
                          type="email"
                          placeholder="Enter your email"
                          value={manualEmail}
                          onChange={(e) => setManualEmail(e.target.value)}
                          required
                        />
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="code">Verification Code</Label>
                        <Input
                          id="code"
                          type="text"
                          placeholder="Enter verification code"
                          value={manualCode}
                          onChange={(e) => setManualCode(e.target.value)}
                          required
                        />
                      </div>
                      
                      <Button 
                        type="submit" 
                        className="w-full"
                        disabled={isVerifying}
                      >
                        {isVerifying ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Verifying...
                          </>
                        ) : (
                          "Verify Email"
                        )}
                      </Button>
                      
                      <Button 
                        type="button"
                        variant="ghost"
                        onClick={() => setShowManualForm(false)}
                        className="w-full"
                      >
                        Back
                      </Button>
                    </form>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
      
      <Footer />
    </div>
  )
}

export default function VerifyEmailPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-slate-950 dark:via-gray-900 dark:to-slate-950 flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading...</span>
        </div>
      </div>
    }>
      <VerifyEmailContent />
    </Suspense>
  )
} 