"use client"

import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Globe, MessageCircle, CheckCircle } from "lucide-react"
import Image from "next/image"

export default function LanguageSupport() {
  const languages = [
    { code: "en", name: "English", flag: "/images/flags/us.png", speakers: "1.5B", native: "Native" },
    { code: "es", name: "Español", flag: "/images/flags/es.png", speakers: "500M", native: "Nativo" },
    { code: "fr", name: "Français", flag: "/images/flags/fr.png", speakers: "280M", native: "Natif" },
    { code: "de", name: "<PERSON><PERSON><PERSON>", flag: "/images/flags/de.png", speakers: "100M", native: "<PERSON><PERSON><PERSON>ch<PERSON>" },
    { code: "it", name: "Italiano", flag: "/images/flags/it.png", speakers: "65M", native: "Madrelingua" },
    { code: "pt", name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", flag: "/images/flags/pt.png", speakers: "260M", native: "Nativo" },
    { code: "ru", name: "<PERSON>усс<PERSON><PERSON>", flag: "/images/flags/ru.png", speakers: "258M", native: "Родной" },
    { code: "zh", name: "中文", flag: "/images/flags/cn.png", speakers: "918M", native: "母语" },
    { code: "ja", name: "日本語", flag: "/images/flags/jp.png", speakers: "125M", native: "ネイティブ" },
    { code: "ko", name: "한국어", flag: "/images/flags/kr.png", speakers: "77M", native: "원어민" },
    { code: "ar", name: "العربية", flag: "/images/flags/sa.png", speakers: "422M", native: "الأصلي" },
    { code: "hi", name: "हिन्दी", flag: "/images/flags/in.png", speakers: "600M", native: "मूल" },
  ]



  return (
    <section className="py-24 md:py-32 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-white to-purple-50/50 dark:from-blue-950/20 dark:via-gray-900 dark:to-purple-950/20" />
      <div className="absolute top-0 left-0 w-full h-full">
        <div className="absolute top-[20%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-r from-blue-500/5 to-cyan-500/5 dark:from-blue-500/10 dark:to-cyan-500/10 blur-3xl animate-pulse" />
        <div
          className="absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-r from-purple-500/5 to-pink-500/5 dark:from-purple-500/10 dark:to-pink-500/10 blur-3xl animate-pulse"
          style={{ animationDelay: "1s" }}
        />
      </div>

      <div className="max-w-6xl mx-auto px-8 md:px-12 lg:px-16 relative z-10">
        {/* Header */}
        <div className="text-center mb-20">
          <Badge className="mb-6 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 border-blue-200 dark:border-blue-400/20 px-4 py-2">
            <Globe className="w-4 h-4 mr-2" />
            Global Support
          </Badge>
          <h2 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-8 leading-tight">
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400 bg-clip-text text-transparent">
              Speak Your
            </span>{" "}
            Language
          </h2>
          <p className="text-lg md:text-xl lg:text-2xl text-gray-700 dark:text-white/70 leading-relaxed max-w-3xl mx-auto">
            Trade with confidence in your native language. Our platform and support team speak your language, ensuring
            you never miss a trading opportunity due to communication barriers.
          </p>
        </div>

        {/* Language Row - Single Row with Horizontal Scrolling */}
        <div className="mb-20">
          <div className="relative overflow-hidden">
            <div className="flex animate-scroll-languages">
              {/* First set of languages */}
              {languages.map((language, index) => (
                <div
                  key={`first-${language.code}`}
                  className="group bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-[1.02] hover:-translate-y-1 mx-4 min-w-[280px] flex-shrink-0"
                  style={{ animationDelay: `${index * 50}ms` }}
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className="relative w-12 h-12 rounded-2xl overflow-hidden shadow-lg group-hover:scale-110 transition-transform duration-300">
                      <Image
                        src={language.flag || "/placeholder.svg"}
                        alt={`${language.name} flag`}
                        fill
                        className="object-cover"
                        sizes="48px"
                      />
                    </div>
                    <div>
                      <h3 className="text-lg font-bold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                        {language.name}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-white/60">{language.speakers} speakers</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span className="text-sm text-gray-700 dark:text-white/70">{language.native}</span>
                  </div>
                </div>
              ))}

              {/* Duplicate set for seamless scrolling */}
              {languages.map((language, index) => (
                <div
                  key={`second-${language.code}`}
                  className="group bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-[1.02] hover:-translate-y-1 mx-4 min-w-[280px] flex-shrink-0"
                  style={{ animationDelay: `${index * 50}ms` }}
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className="relative w-12 h-12 rounded-2xl overflow-hidden shadow-lg group-hover:scale-110 transition-transform duration-300">
                      <Image
                        src={language.flag || "/placeholder.svg"}
                        alt={`${language.name} flag`}
                        fill
                        className="object-cover"
                        sizes="48px"
                      />
                    </div>
                    <div>
                      <h3 className="text-lg font-bold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                        {language.name}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-white/60">{language.speakers} speakers</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span className="text-sm text-gray-700 dark:text-white/70">{language.native}</span>
                  </div>
                </div>
              ))}

              {/* Third set for extra seamless scrolling */}
              {languages.map((language, index) => (
                <div
                  key={`third-${language.code}`}
                  className="group bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-[1.02] hover:-translate-y-1 mx-4 min-w-[280px] flex-shrink-0"
                  style={{ animationDelay: `${index * 50}ms` }}
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className="relative w-12 h-12 rounded-2xl overflow-hidden shadow-lg group-hover:scale-110 transition-transform duration-300">
                      <Image
                        src={language.flag || "/placeholder.svg"}
                        alt={`${language.name} flag`}
                        fill
                        className="object-cover"
                        sizes="48px"
                      />
                    </div>
                    <div>
                      <h3 className="text-lg font-bold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                        {language.name}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-white/60">{language.speakers} speakers</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span className="text-sm text-gray-700 dark:text-white/70">{language.native}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>



        {/* Stats Section */}
        <div className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 dark:from-blue-500/20 dark:to-purple-500/20 backdrop-blur-sm border border-blue-200 dark:border-blue-400/20 rounded-3xl p-8 md:p-12 shadow-xl">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div className="group">
              <div className="text-4xl md:text-5xl font-bold text-blue-600 dark:text-blue-400 mb-2 group-hover:scale-110 transition-transform duration-300">
                50+
              </div>
              <div className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Countries Supported</div>
              <div className="text-gray-700 dark:text-white/70">Global reach with local expertise</div>
            </div>
            <div className="group">
              <div className="text-4xl md:text-5xl font-bold text-purple-600 dark:text-purple-400 mb-2 group-hover:scale-110 transition-transform duration-300">
                12
              </div>
              <div className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Languages Available</div>
              <div className="text-gray-700 dark:text-white/70">Native support in major languages</div>
            </div>
            <div className="group">
              <div className="text-4xl md:text-5xl font-bold text-cyan-600 dark:text-cyan-400 mb-2 group-hover:scale-110 transition-transform duration-300">
                24/7
              </div>
              <div className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Support Available</div>
              <div className="text-gray-700 dark:text-white/70">Round-the-clock assistance</div>
            </div>
          </div>
        </div>

        {/* CTA */}
        <div className="text-center mt-16">
          <Button className="bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-500 dark:to-purple-500 text-white hover:from-blue-700 hover:to-purple-700 dark:hover:from-blue-600 dark:hover:to-purple-600 px-8 py-4 text-lg hover:scale-105 transition-all duration-300 hover:shadow-lg">
            <MessageCircle className="w-5 h-5 mr-2" />
            Start Trading in Your Language
          </Button>
        </div>
      </div>

      {/* Custom CSS for horizontal scrolling animation */}
      <style jsx>{`
        @keyframes scroll-languages {
          0% {
            transform: translateX(0);
          }
          100% {
            transform: translateX(-33.333%);
          }
        }
        
        .animate-scroll-languages {
          animation: scroll-languages 25s linear infinite;
          will-change: transform;
        }
        
        .animate-scroll-languages:hover {
          animation-play-state: paused;
        }
      `}</style>
    </section>
  )
}
