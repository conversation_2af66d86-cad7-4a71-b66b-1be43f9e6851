"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON>R<PERSON>, TrendingUp, Moon, Sun, DollarSign, Zap, Users, Award, Globe, User, LogIn, Flame, Gift, Newspaper, CheckCircle, Star, Target } from "lucide-react"
import { useState, useEffect, useRef } from "react"
import Link from "next/link"
import { useCounter } from "@/hooks/use-counter"
import Footer from "@/components/footer"
import LanguageSelector from "@/components/language-selector"
import { useLanguage } from "@/contexts/language-context"

export default function HowItWorksPage() {
  const [isDarkMode, setIsDarkMode] = useState(true)
  const { t, isRTL } = useLanguage()

  // Check system preference on initial load
  useEffect(() => {
    if (typeof window !== "undefined") {
      const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches
      setIsDarkMode(prefersDark)
    }
  }, [])

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode)
    document.documentElement.classList.toggle("dark")
  }

  return (
    <div
      className={`min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-slate-950 dark:via-gray-900 dark:to-slate-950 text-gray-900 dark:text-white overflow-hidden relative transition-colors duration-500 ${isRTL ? "rtl" : "ltr"}`}
    >
      {/* Custom CSS for enhanced UX */}
      <style jsx global>{`
        ::selection {
          background: ${isDarkMode ? "rgba(59, 130, 246, 0.3)" : "rgba(37, 99, 235, 0.2)"};
          color: ${isDarkMode ? "#ffffff" : "#1f2937"};
        }
        
        /* Custom scrollbar */
        ::-webkit-scrollbar {
          width: 6px;
        }
        ::-webkit-scrollbar-track {
          background: ${isDarkMode ? "rgba(15, 23, 42, 0.1)" : "rgba(243, 244, 246, 0.5)"};
        }
        ::-webkit-scrollbar-thumb {
          background: ${isDarkMode ? "rgba(59, 130, 246, 0.3)" : "rgba(37, 99, 235, 0.3)"};
          border-radius: 3px;
        }
        ::-webkit-scrollbar-thumb:hover {
          background: ${isDarkMode ? "rgba(59, 130, 246, 0.5)" : "rgba(37, 99, 235, 0.5)"};
        }

        /* Breathing animation */
        @keyframes subtle-breathe {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.01); }
        }
        
        .subtle-breathe {
          animation: subtle-breathe 6s ease-in-out infinite;
          will-change: transform;
        }

        /* Hardware acceleration for performance */
        .hw-accelerate {
          transform: translateZ(0);
          will-change: transform;
        }
      `}</style>

      {/* Artistic Background */}
      <div className="fixed inset-0 bg-[radial-gradient(ellipse_at_center,rgba(59,130,246,0.05),rgba(255,255,255,0))] dark:bg-[radial-gradient(ellipse_at_center,rgba(59,130,246,0.15),rgba(0,0,0,0))]" />
      <div className="fixed top-0 left-0 w-full h-full">
        <div className="absolute top-[10%] left-[5%] w-32 md:w-64 h-32 md:h-64 rounded-full bg-gradient-to-r from-blue-500/5 to-cyan-500/5 dark:from-blue-500/10 dark:to-cyan-500/10 blur-3xl subtle-breathe" />
        <div
          className="absolute top-[40%] right-[10%] w-40 md:w-80 h-40 md:h-80 rounded-full bg-gradient-to-r from-indigo-500/5 to-blue-500/5 dark:from-indigo-500/10 dark:to-blue-500/10 blur-3xl subtle-breathe"
          style={{ animationDelay: "1s" }}
        />
        <div
          className="absolute bottom-[15%] left-[15%] w-36 md:w-72 h-36 md:h-72 rounded-full bg-gradient-to-r from-cyan-500/5 to-teal-500/5 dark:from-cyan-500/10 dark:to-teal-500/10 blur-3xl subtle-breathe"
          style={{ animationDelay: "2s" }}
        />
      </div>

      {/* Main Content */}
      <main className="relative z-10">
        {/* Responsive Navigation */}
        <nav
          className="fixed top-4 left-4 right-4 z-40 flex items-center justify-between bg-transparent backdrop-blur-sm border border-gray-200/30 dark:border-white/10 rounded-2xl px-3 py-2 shadow-sm"
          role="navigation"
          aria-label="Main navigation"
        >
          {/* Logo */}
          <Link href="/" className="flex items-center gap-2 group">
            <div className="w-8 h-8 md:w-10 md:h-10 rounded-xl bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
              <TrendingUp className="w-4 h-4 md:w-5 md:h-5 text-white" />
            </div>
            <div className="hidden sm:block">
              <h1 className="text-lg md:text-xl font-bold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                Forex Throne
              </h1>
              <p className="text-xs text-gray-600 dark:text-white/60">Prop Trading Firm</p>
            </div>
          </Link>

          {/* Navigation Links */}
          <div className="flex items-center gap-2 md:gap-4">
            <LanguageSelector />
            <Button
              variant="ghost"
              onClick={toggleTheme}
              className="text-xs md:text-sm font-light text-gray-700 dark:text-white/80 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/30 dark:hover:bg-white/5 transition-all duration-300 px-2 py-1 rounded-lg group"
              aria-label="Toggle between light and dark theme"
            >
              <div className="group-hover:rotate-180 transition-transform duration-500">
                {isDarkMode ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
              </div>
            </Button>
            <Link href="/trading-symbols">
              <Button
                variant="ghost"
                className="text-xs md:text-sm font-light text-gray-700 dark:text-white/80 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/30 dark:hover:bg-white/5 transition-all duration-300 px-2 py-1 rounded-lg"
              >
                {t("nav.markets")}
              </Button>
            </Link>
            <Link href="/economic-calendar">
              <Button
                variant="ghost"
                className="text-xs md:text-sm font-light text-gray-700 dark:text-white/80 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/30 dark:hover:bg-white/5 transition-all duration-300 px-2 py-1 rounded-lg"
              >
                {t("nav.calendar")}
              </Button>
            </Link>
            <Link href="/how-it-works">
              <Button
                variant="ghost"
                className="text-xs md:text-sm font-light text-gray-700 dark:text-white/80 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/30 dark:hover:bg-white/5 transition-all duration-300 px-2 py-1 rounded-lg"
              >
                How It Works
              </Button>
            </Link>
            <Link href="/challenges">
              <Button
                variant="ghost"
                className="text-xs md:text-sm font-light text-gray-700 dark:text-white/80 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/30 dark:hover:bg-white/5 transition-all duration-300 px-2 py-1 rounded-lg"
              >
                Challenges
              </Button>
            </Link>
            <Link href="/affiliate">
              <Button
                variant="ghost"
                className="text-xs md:text-sm font-light text-gray-700 dark:text-white/80 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/30 dark:hover:bg-white/5 transition-all duration-300 px-2 py-1 rounded-lg"
              >
                {t("nav.affiliate")}
              </Button>
            </Link>
            <Link href="/giveaway">
              <Button
                variant="ghost"
                className="text-xs md:text-sm font-light text-gray-700 dark:text-white/80 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/30 dark:hover:bg-white/5 transition-all duration-300 px-2 py-1 rounded-lg"
              >
                <Gift className="h-3 w-3 mr-1" />
                Giveaway
              </Button>
            </Link>
            <Link href="/news">
              <Button
                variant="ghost"
                className="text-xs md:text-sm font-light text-gray-700 dark:text-white/80 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/30 dark:hover:bg-white/5 transition-all duration-300 px-2 py-1 rounded-lg"
              >
                <Newspaper className="h-3 w-3 mr-1" />
                News
              </Button>
            </Link>
            <Link href="/auth">
              <Button
                variant="ghost"
                className="text-xs md:text-sm font-light text-gray-700 dark:text-white/80 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/30 dark:hover:bg-white/5 transition-all duration-300 px-2 py-1 rounded-lg"
              >
                <LogIn className="h-3 w-3 mr-1" />
                {t("nav.login")}
              </Button>
            </Link>
            <Link href="/auth?mode=signup">
              <Button className="rounded-lg bg-gray-900 dark:bg-white text-white dark:text-black hover:bg-gray-800 dark:hover:bg-white/90 px-2 md:px-4 py-1 text-xs md:text-sm hover:scale-105 transition-all duration-300 hover:shadow-lg">
                <User className="h-3 w-3 mr-1" />
                {t("nav.getFunded")}
              </Button>
            </Link>
          </div>
        </nav>

        {/* Hero Section */}
        <section className="pt-24 md:pt-32 lg:pt-40 pb-16">
          <div className="max-w-6xl mx-auto px-8 md:px-12 lg:px-16 text-center">
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-8 leading-tight text-gray-900 dark:text-white">
              How to Get Funded as a{" "}
              <span className="bg-gradient-to-r from-blue-600 via-cyan-600 to-indigo-600 dark:from-blue-400 dark:via-cyan-400 dark:to-indigo-400 bg-clip-text text-transparent">
                Forex Trader
              </span>
            </h1>
            <p className="text-lg md:text-xl lg:text-2xl text-gray-700 dark:text-white/80 mb-12 max-w-3xl mx-auto leading-relaxed">
              Learn our prop firm challenge requirements and trading evaluation criteria through our 3-step educational process using simulated accounts for skill-based programs.
            </p>
          </div>
        </section>

        {/* How Evaluation Challenges Work Section */}
        <section className="py-8 md:py-12 relative">
          <div className="max-w-6xl mx-auto px-8 md:px-12 lg:px-16">
            {/* Section Header */}
            <div className="text-center mb-8 md:mb-12">
              <h2 className="text-2xl md:text-4xl lg:text-5xl font-bold mb-4 md:mb-6 leading-tight text-gray-900 dark:text-white">
                How our challenges work
              </h2>
            </div>

            {/* Three Steps */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6 mb-8">
              {/* Step 1: Select Your Challenge */}
              <div className="text-center group">
                <div className="w-12 h-12 md:w-16 md:h-16 rounded-xl bg-gradient-to-r from-blue-600 via-cyan-600 to-indigo-600 dark:from-blue-500 dark:via-cyan-500 dark:to-indigo-500 flex items-center justify-center mx-auto mb-3 shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <svg className="w-6 h-6 md:w-8 md:h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M7 18c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2-.9-2-2-2zM1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12L8.1 13h7.45c.75 0 1.41-.41 1.75-1.03L21.7 4H5.21l-.94-2H1zm16 16c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2z"/>
                  </svg>
                </div>
                <h3 className="text-base md:text-lg font-bold text-gray-900 dark:text-white mb-2">
                  Select Your Challenge
                </h3>
                <p className="text-gray-700 dark:text-white/70 text-xs leading-relaxed">
                  Explore a variety of structured trading evaluations tailored to different strategies, experience levels, and goals.
                </p>
              </div>

              {/* Step 2: Showcase Your Expertise */}
              <div className="text-center group">
                <div className="w-12 h-12 md:w-16 md:h-16 rounded-xl bg-gradient-to-r from-blue-600 via-cyan-600 to-indigo-600 dark:from-blue-500 dark:via-cyan-500 dark:to-indigo-500 flex items-center justify-center mx-auto mb-3 shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <svg className="w-6 h-6 md:w-8 md:h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                </div>
                <h3 className="text-base md:text-lg font-bold text-gray-900 dark:text-white mb-2">
                  Showcase Your Expertise
                </h3>
                <p className="text-gray-700 dark:text-white/70 text-xs leading-relaxed">
                  Demonstrate your trading skillset by navigating real market conditions in a risk-managed, rules-based simulated environment.
                </p>
              </div>

              {/* Step 3: Access Higher Tier Simulated Accounts */}
              <div className="text-center group">
                <div className="w-12 h-12 md:w-16 md:h-16 rounded-xl bg-gradient-to-r from-blue-600 via-cyan-600 to-indigo-600 dark:from-blue-500 dark:via-cyan-500 dark:to-indigo-500 flex items-center justify-center mx-auto mb-3 shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <svg className="w-6 h-6 md:w-8 md:h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                </div>
                <h3 className="text-base md:text-lg font-bold text-gray-900 dark:text-white mb-2">
                  Access Higher Tier Simulated Accounts
                </h3>
                <p className="text-gray-700 dark:text-white/70 text-xs leading-relaxed">
                  Upon successful completion, gain access to advanced evaluation accounts — designed to mirror professional conditions for educational and skill assessment purposes only.
                </p>
              </div>
            </div>

            {/* CTA Button */}
            <div className="text-center">
              <Link href="/challenges">
                <Button className="bg-gradient-to-r from-blue-600 via-cyan-600 to-indigo-600 dark:from-blue-500 dark:via-cyan-500 dark:to-indigo-500 hover:from-blue-700 hover:via-cyan-700 hover:to-indigo-700 text-white px-4 md:px-6 py-2 md:py-3 text-sm md:text-base font-semibold rounded-full hover:scale-105 transition-all duration-300 hover:shadow-xl">
                  Choose Your Challenge
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
            </div>
          </div>
        </section>

        {/* Key Features Section */}
        <section className="py-16 md:py-24 bg-white/50 dark:bg-white/5">
          <div className="max-w-6xl mx-auto px-8 md:px-12 lg:px-16">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-gray-900 dark:text-white">
                Why Choose Our Trader Funding Program?
              </h2>
              <p className="text-lg md:text-xl text-gray-700 dark:text-white/80 max-w-3xl mx-auto">
                Discover the advantages of our educational forex evaluation program and performance-based assessment designed for skill development.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
              {/* Feature 1 */}
              <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm rounded-2xl p-6 border border-gray-200/50 dark:border-white/10">
                <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center mb-4">
                  <Target className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                  Rapid Trading Skill Assessment
                </h3>
                <p className="text-gray-700 dark:text-white/70 text-sm leading-relaxed">
                  Complete your performance-based assessment in as little as 3 trading days through our educational simulated trading account program.
                </p>
              </div>

              {/* Feature 2 */}
              <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm rounded-2xl p-6 border border-gray-200/50 dark:border-white/10">
                <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center mb-4">
                  <DollarSign className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                  Up to $600K Funding
                </h3>
                <p className="text-gray-700 dark:text-white/70 text-sm leading-relaxed">
                  Access the highest funding levels in the industry with our competitive allocation limits.
                </p>
              </div>

              {/* Feature 3 */}
              <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm rounded-2xl p-6 border border-gray-200/50 dark:border-white/10">
                <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center mb-4">
                  <CheckCircle className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                  Weekend Holding
                </h3>
                <p className="text-gray-700 dark:text-white/70 text-sm leading-relaxed">
                  Hold positions over the weekend with our flexible trading rules and conditions.
                </p>
              </div>

              {/* Feature 4 */}
              <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm rounded-2xl p-6 border border-gray-200/50 dark:border-white/10">
                <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center mb-4">
                  <Star className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                  One-Step Challenge
                </h3>
                <p className="text-gray-700 dark:text-white/70 text-sm leading-relaxed">
                  Skip the traditional two-phase process with our simplified one-step evaluation.
                </p>
              </div>

              {/* Feature 5 */}
              <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm rounded-2xl p-6 border border-gray-200/50 dark:border-white/10">
                <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center mb-4">
                  <Users className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                  Community Support
                </h3>
                <p className="text-gray-700 dark:text-white/70 text-sm leading-relaxed">
                  Join our thriving community of funded traders and access exclusive resources.
                </p>
              </div>

              {/* Feature 6 */}
              <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm rounded-2xl p-6 border border-gray-200/50 dark:border-white/10">
                <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center mb-4">
                  <Award className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                  Broker Backed
                </h3>
                <p className="text-gray-700 dark:text-white/70 text-sm leading-relaxed">
                  Trade with confidence knowing we're backed by established broker partnerships.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="py-16 md:py-24">
          <div className="max-w-4xl mx-auto text-center px-8 md:px-12 lg:px-16">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-8 text-gray-900 dark:text-white">
              Ready to Start Your Trading Challenge?
            </h2>
            <p className="text-lg md:text-xl text-gray-700 dark:text-white/80 mb-8">
              Begin your prop firm evaluation today and access performance-based funding opportunities through our educational skill-based programs.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/challenges">
                <Button className="bg-gradient-to-r from-blue-600 via-cyan-600 to-indigo-600 dark:from-blue-500 dark:via-cyan-500 dark:to-indigo-500 hover:from-blue-700 hover:via-cyan-700 hover:to-indigo-700 text-white px-6 py-3 text-lg font-semibold rounded-full hover:scale-105 transition-all duration-300 hover:shadow-xl">
                  View Challenges
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link href="/faqs">
                <Button variant="outline" className="border-2 border-gray-300 dark:border-white/20 text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-white/5 px-6 py-3 text-lg font-semibold rounded-full hover:scale-105 transition-all duration-300">
                  Learn More
                </Button>
              </Link>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <Footer />
    </div>
  )
} 