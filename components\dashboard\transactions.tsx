"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Search, Filter, Download, ArrowUpDown } from "lucide-react"
import { useState } from "react"
import { useLanguage } from "@/contexts/language-context"

export default function Transactions() {
  const { t } = useLanguage()
  const [searchTerm, setSearchTerm] = useState("")
  const [filterType, setFilterType] = useState("all")

  const transactions = [
    {
      id: "TXN001",
      date: "2024-01-15",
      type: "deposit",
      amount: 1000,
      status: "completed",
      reference: "DEP-2024-001",
    },
    {
      id: "TXN002",
      date: "2024-01-16",
      type: "profit",
      amount: 250,
      status: "completed",
      reference: "PRF-2024-001",
    },
    {
      id: "TXN003",
      date: "2024-01-17",
      type: "withdrawal",
      amount: -500,
      status: "pending",
      reference: "WTH-2024-001",
    },
    {
      id: "TXN004",
      date: "2024-01-18",
      type: "fee",
      amount: -25,
      status: "completed",
      reference: "FEE-2024-001",
    },
    {
      id: "TXN005",
      date: "2024-01-19",
      type: "profit",
      amount: 180,
      status: "completed",
      reference: "PRF-2024-002",
    },
    {
      id: "TXN006",
      date: "2024-01-20",
      type: "withdrawal",
      amount: -300,
      status: "processing",
      reference: "WTH-2024-002",
    },
  ]

  const getTypeColor = (type: string) => {
    switch (type) {
      case "deposit":
        return "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400"
      case "withdrawal":
        return "bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400"
      case "profit":
        return "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400"
      case "fee":
        return "bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-400"
      default:
        return "bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-400"
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400"
      case "pending":
        return "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400"
      case "processing":
        return "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400"
      case "failed":
        return "bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400"
      default:
        return "bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-400"
    }
  }

  const getTypeText = (type: string) => {
    switch (type) {
      case "deposit":
        return t("transactions.deposit")
      case "withdrawal":
        return t("transactions.withdrawal")
      case "profit":
        return t("transactions.profit")
      case "fee":
        return t("transactions.fee")
      default:
        return type
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case "completed":
        return t("transactions.completed")
      case "pending":
        return t("transactions.pending")
      case "processing":
        return t("transactions.processing")
      case "failed":
        return t("transactions.failed")
      default:
        return status
    }
  }

  const filteredTransactions = transactions.filter((transaction) => {
    const matchesSearch =
      transaction.reference.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.id.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesFilter = filterType === "all" || transaction.type === filterType
    return matchesSearch && matchesFilter
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">{t("transactions.title")}</h1>
        <p className="text-gray-600 dark:text-gray-400">{t("transactions.subtitle")}</p>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder={t("transactions.search")}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="flex gap-2">
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="px-4 py-2 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
          >
            <option value="all">{t("transactions.filter.all")}</option>
            <option value="deposit">{t("transactions.filter.deposits")}</option>
            <option value="withdrawal">{t("transactions.filter.withdrawals")}</option>
            <option value="profit">{t("transactions.filter.profits")}</option>
            <option value="fee">{t("transactions.filter.fees")}</option>
          </select>
          <Button variant="outline" size="sm">
            <Filter className="w-4 h-4 mr-2" />
            {t("common.filter")}
          </Button>
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            {t("common.export")}
          </Button>
        </div>
      </div>

      {/* Transactions Table */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-900/50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  <div className="flex items-center gap-1">
                    {t("transactions.date")}
                    <ArrowUpDown className="w-3 h-3" />
                  </div>
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {t("transactions.type")}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {t("transactions.amount")}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {t("transactions.status")}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {t("transactions.reference")}
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
              {filteredTransactions.length > 0 ? (
                filteredTransactions.map((transaction) => (
                  <tr key={transaction.id} className="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {transaction.date}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Badge className={getTypeColor(transaction.type)}>{getTypeText(transaction.type)}</Badge>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <span
                        className={
                          transaction.amount > 0
                            ? "text-green-600 dark:text-green-400"
                            : "text-red-600 dark:text-red-400"
                        }
                      >
                        {transaction.amount > 0 ? "+" : ""}${Math.abs(transaction.amount).toLocaleString()}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Badge className={getStatusColor(transaction.status)}>{getStatusText(transaction.status)}</Badge>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {transaction.reference}
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={5} className="px-6 py-12 text-center">
                    <div className="text-gray-500 dark:text-gray-400">
                      <p className="text-lg font-medium mb-2">{t("transactions.noResults")}</p>
                      <p className="text-sm">{t("transactions.tryDifferent")}</p>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
