"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { ChevronDown, HelpCircle, Shield, DollarSign, Clock, Users, TrendingUp, Award, Zap, Globe } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import Link from "next/link"

interface FAQItem {
  id: number
  question: string
  answer: string
  category: string
  icon: React.ReactNode
}

const faqData: FAQItem[] = [
  {
    id: 1,
    question: "What is a prop trading challenge and how does it work?",
    answer: "A prop trading challenge is a structured evaluation program where traders demonstrate their skills using simulated capital. Successful completion leads to funding with real capital. The process involves meeting specific profit targets while adhering to risk management rules within defined timeframes. Our challenges are designed to identify consistently profitable traders who can manage risk effectively.",
    category: "General",
    icon: <HelpCircle className="w-5 h-5" />
  },
  {
    id: 2,
    question: "What are the profit targets and drawdown limits?",
    answer: "Profit targets vary by challenge type: Standard challenges require 8% profit target with 5% maximum drawdown, while Rapid challenges require 10% profit target with 6% maximum drawdown. Premium challenges offer higher targets with enhanced features. All limits are clearly defined in your challenge agreement and monitored in real-time through our advanced tracking system.",
    category: "Trading Rules",
    icon: <TrendingUp className="w-5 h-5" />
  },
  {
    id: 3,
    question: "How long do I have to complete the challenge?",
    answer: "Challenge durations are flexible and vary by program: Standard challenges typically allow 30-60 days, Rapid challenges offer 14-30 days, and Premium challenges provide extended timeframes with additional benefits. There are no strict time limits - you can take as long as needed to demonstrate consistent trading performance while meeting the required targets.",
    category: "Timeline",
    icon: <Clock className="w-5 h-5" />
  },
  {
    id: 4,
    question: "What happens after I successfully complete the challenge?",
    answer: "Upon successful challenge completion, you'll receive a funded trading account with real capital. You'll keep 90% of all profits generated, with 10% going to the platform. There are no monthly fees or hidden costs. You'll also gain access to our comprehensive support system, advanced trading tools, and community resources to maximize your success.",
    category: "Funding",
    icon: <DollarSign className="w-5 h-5" />
  },
  {
    id: 5,
    question: "What trading instruments and markets can I trade?",
    answer: "Our funded accounts provide access to major forex pairs, indices, commodities, and cryptocurrencies. You can trade during all major market sessions with competitive spreads and execution speeds. Our platform supports multiple trading strategies including scalping, day trading, and swing trading. All instruments are clearly listed in your trading dashboard with real-time pricing and analysis tools.",
    category: "Trading",
    icon: <Globe className="w-5 h-5" />
  },
  {
    id: 6,
    question: "Is there a monthly fee or subscription cost?",
    answer: "No, there are no monthly fees or subscription costs. The one-time challenge fee covers your evaluation period. Once funded, you keep 90% of profits with no ongoing charges. We believe in aligning our success with yours - we only profit when you profit. This transparent model ensures our interests are directly aligned with your trading success.",
    category: "Pricing",
    icon: <Shield className="w-5 h-5" />
  },
  {
    id: 7,
    question: "What support and resources are available to traders?",
    answer: "Our comprehensive support system includes 24/7 customer service, dedicated account managers, educational resources, trading webinars, and access to our community of funded traders. We provide advanced trading tools, market analysis, and personalized guidance to help you maximize your potential. Our team of experienced professionals is committed to your success.",
    category: "Support",
    icon: <Users className="w-5 h-5" />
  },
  {
    id: 8,
    question: "Can I withdraw my profits and how often?",
    answer: "Yes, you can withdraw your profits monthly once you've met the minimum withdrawal threshold. Withdrawals are processed within 1-3 business days to your registered payment method. There are no withdrawal fees, and you maintain full control over your profits. Our transparent profit-sharing model ensures you receive your earnings promptly and securely.",
    category: "Withdrawals",
    icon: <Award className="w-5 h-5" />
  },
  {
    id: 9,
    question: "What happens if I exceed the drawdown limit?",
    answer: "If you exceed the maximum drawdown limit, your challenge will be reset. You'll have the option to restart with a fresh evaluation period. We provide detailed feedback on your trading performance and offer guidance on risk management strategies. Our goal is to help you develop the skills needed for consistent profitability in funded trading.",
    category: "Risk Management",
    icon: <Zap className="w-5 h-5" />
  },
  {
    id: 10,
    question: "How do I get started with my trading challenge?",
    answer: "Getting started is simple: Choose your preferred challenge type and account size, complete the registration process, and you'll receive immediate access to your evaluation account. Our platform provides comprehensive onboarding, including tutorial videos, trading guidelines, and 24/7 support. You can begin trading immediately with full access to all features and tools.",
    category: "Getting Started",
    icon: <TrendingUp className="w-5 h-5" />
  }
]

const categories = ["All", "General", "Trading Rules", "Timeline", "Funding", "Trading", "Pricing", "Support", "Withdrawals", "Risk Management", "Getting Started"]

export default function FAQSection() {
  const [openItems, setOpenItems] = useState<number[]>([])
  const [selectedCategory, setSelectedCategory] = useState("All")

  const filteredFAQs = selectedCategory === "All" 
    ? faqData 
    : faqData.filter(faq => faq.category === selectedCategory)

  // Default open first two items for better UX, and reset when category changes
  useEffect(() => {
    setOpenItems(filteredFAQs.slice(0, 2).map(f => f.id))
  }, [selectedCategory])

  const toggleItem = (id: number) => {
    setOpenItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    )
  }

  const expandAll = () => setOpenItems(filteredFAQs.map(f => f.id))
  const collapseAll = () => setOpenItems([])

  return (
    <section className="py-16 md:py-24 bg-gradient-to-br from-gray-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-slate-900">
      <div className="max-w-6xl mx-auto px-8 md:px-12 lg:px-16">
        {/* Header */}
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="inline-flex items-center gap-2 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-4 py-2 rounded-full text-sm font-medium mb-6"
          >
            <HelpCircle className="w-4 h-4" />
            Frequently Asked Questions
          </motion.div>
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-gray-900 dark:text-white"
          >
            Everything You Need to Know
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="text-lg md:text-xl text-gray-600 dark:text-white/70 max-w-3xl mx-auto leading-relaxed"
          >
            Comprehensive answers to help you understand our prop trading challenges and funding program
          </motion.p>
        </div>

        {/* Category Filter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="flex flex-wrap justify-center gap-2 mb-6"
        >
          {categories.map((category) => (
            <Button
              key={category}
              variant={selectedCategory === category ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory(category)}
              className={`rounded-full px-4 py-2 text-sm font-medium transition-all duration-300 ${
                selectedCategory === category
                  ? "bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg"
                  : "border-gray-300 dark:border-gray-600 text-gray-700 dark:text-white/70 hover:bg-gray-100 dark:hover:bg-gray-800"
              }`}
            >
              {category}
            </Button>
          ))}
        </motion.div>

        {/* Controls */}
        <div className="flex justify-center gap-2 mb-8">
          <Button size="sm" variant="outline" onClick={expandAll} className="rounded-full">
            Expand All
          </Button>
          <Button size="sm" variant="outline" onClick={collapseAll} className="rounded-full">
            Collapse All
          </Button>
        </div>

        {/* FAQ Items */}
        <div className="space-y-4">
          <AnimatePresence mode="wait">
            {filteredFAQs.map((faq, index) => (
              <motion.div
                key={faq.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.5, delay: index * 0.05 }}
              >
                <Card className="border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-300 overflow-hidden">
                  <CardContent className="p-0">
                    <button
                      onClick={() => toggleItem(faq.id)}
                      className="w-full px-6 py-6 text-left flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors duration-200"
                    >
                      <div className="flex items-center gap-4">
                        <div className="text-blue-600 dark:text-blue-400">
                          {faq.icon}
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1">
                            {faq.question}
                          </h3>
                          <span className="text-xs text-blue-600 dark:text-blue-400 font-medium">
                            {faq.category}
                          </span>
                        </div>
                      </div>
                      <motion.div
                        animate={{ rotate: openItems.includes(faq.id) ? 180 : 0 }}
                        transition={{ duration: 0.3 }}
                        className="text-gray-500 dark:text-gray-400"
                      >
                        <ChevronDown className="w-5 h-5" />
                      </motion.div>
                    </button>
                    
                    <AnimatePresence>
                      {openItems.includes(faq.id) && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: "auto", opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.3, ease: "easeInOut" }}
                          className="overflow-hidden"
                        >
                          <div className="px-6 pb-6 pt-0">
                            <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                              <p className="text-gray-600 dark:text-white/70 leading-relaxed">
                                {faq.answer}
                              </p>
                            </div>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20 backdrop-blur-sm border border-blue-200 dark:border-blue-400/20 rounded-3xl p-12 shadow-2xl">
            <h3 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Ready to Start Your Trading Journey?
            </h3>
            <p className="text-lg text-gray-600 dark:text-white/70 mb-8 max-w-2xl mx-auto">
              Join thousands of successful traders who have already been funded through our program
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/auth?mode=signup">
                <Button className="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white px-8 py-4 text-lg font-semibold rounded-xl hover:scale-105 transition-all duration-300 shadow-lg">
                  Start Your Challenge
                </Button>
              </Link>
              <Link href="/contact">
                <Button variant="outline" className="border-2 border-gray-300 dark:border-white/20 text-gray-700 dark:text-white/70 hover:bg-gray-50 dark:hover:bg-white/5 px-8 py-4 text-lg font-semibold rounded-xl">
                  Contact Support
                </Button>
              </Link>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}