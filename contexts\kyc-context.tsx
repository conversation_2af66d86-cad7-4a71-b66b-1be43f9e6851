"use client"

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'

export type KYCStatus = 'not_started' | 'in_progress' | 'completed' | 'rejected'

export interface KYCData {
  status: KYCStatus
  currentStep: number
  completedSteps: number[]
  personalInfo: {
    firstName: string
    lastName: string
    dateOfBirth: string
    nationality: string
    address: string
  }
  documents: {
    governmentId: File | null
    proofOfAddress: File | null
  }
  evaluationChallenges: {
    riskManagement: 'not_started' | 'in_progress' | 'completed' | 'failed'
    marketAnalysis: 'not_started' | 'in_progress' | 'completed' | 'failed'
    tradingPsychology: 'not_started' | 'in_progress' | 'completed' | 'failed'
    complianceEthics: 'not_started' | 'in_progress' | 'completed' | 'failed'
  }
  submissionDate: string | null
  approvalDate: string | null
  rejectionReason: string | null
}

interface KYCContextType {
  kycData: KYCData
  updateKYCStatus: (status: KYCStatus) => void
  updateCurrentStep: (step: number) => void
  updatePersonalInfo: (info: Partial<KYCData['personalInfo']>) => void
  updateDocuments: (documents: Partial<KYCData['documents']>) => void
  updateEvaluationChallenge: (challenge: keyof KYCData['evaluationChallenges'], status: KYCData['evaluationChallenges'][keyof KYCData['evaluationChallenges']]) => void
  submitKYC: () => void
  resetKYC: () => void
  isKYCCompleted: boolean
  canWithdraw: boolean
}

const defaultKYCData: KYCData = {
  status: 'not_started',
  currentStep: 1,
  completedSteps: [],
  personalInfo: {
    firstName: '',
    lastName: '',
    dateOfBirth: '',
    nationality: '',
    address: '',
  },
  documents: {
    governmentId: null,
    proofOfAddress: null,
  },
  evaluationChallenges: {
    riskManagement: 'not_started',
    marketAnalysis: 'not_started',
    tradingPsychology: 'not_started',
    complianceEthics: 'not_started',
  },
  submissionDate: null,
  approvalDate: null,
  rejectionReason: null,
}

const KYCContext = createContext<KYCContextType | undefined>(undefined)

export function KYCProvider({ children }: { children: ReactNode }) {
  const [kycData, setKYCData] = useState<KYCData>(defaultKYCData)

  // Load KYC data from localStorage on mount
  useEffect(() => {
    const savedKYCData = localStorage.getItem('kycData')
    if (savedKYCData) {
      try {
        const parsedData = JSON.parse(savedKYCData)
        setKYCData(parsedData)
      } catch (error) {
        console.error('Error parsing saved KYC data:', error)
      }
    }
  }, [])

  // Save KYC data to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('kycData', JSON.stringify(kycData))
  }, [kycData])

  const updateKYCStatus = (status: KYCStatus) => {
    setKYCData(prev => ({
      ...prev,
      status,
      ...(status === 'in_progress' && { submissionDate: new Date().toISOString() }),
      ...(status === 'completed' && { approvalDate: new Date().toISOString() }),
    }))
  }

  const updateCurrentStep = (step: number) => {
    setKYCData(prev => ({
      ...prev,
      currentStep: step,
    }))
  }

  const updatePersonalInfo = (info: Partial<KYCData['personalInfo']>) => {
    setKYCData(prev => ({
      ...prev,
      personalInfo: { ...prev.personalInfo, ...info },
    }))
  }

  const updateDocuments = (documents: Partial<KYCData['documents']>) => {
    setKYCData(prev => ({
      ...prev,
      documents: { ...prev.documents, ...documents },
    }))
  }

  const updateEvaluationChallenge = (
    challenge: keyof KYCData['evaluationChallenges'], 
    status: KYCData['evaluationChallenges'][keyof KYCData['evaluationChallenges']]
  ) => {
    setKYCData(prev => ({
      ...prev,
      evaluationChallenges: {
        ...prev.evaluationChallenges,
        [challenge]: status,
      },
    }))
  }

  const submitKYC = () => {
    setKYCData(prev => ({
      ...prev,
      status: 'in_progress',
      submissionDate: new Date().toISOString(),
    }))
  }

  const resetKYC = () => {
    setKYCData(defaultKYCData)
    localStorage.removeItem('kycData')
  }

  const isKYCCompleted = kycData.status === 'completed'
  const canWithdraw = isKYCCompleted

  const value: KYCContextType = {
    kycData,
    updateKYCStatus,
    updateCurrentStep,
    updatePersonalInfo,
    updateDocuments,
    updateEvaluationChallenge,
    submitKYC,
    resetKYC,
    isKYCCompleted,
    canWithdraw,
  }

  return (
    <KYCContext.Provider value={value}>
      {children}
    </KYCContext.Provider>
  )
}

export function useKYC() {
  const context = useContext(KYCContext)
  if (context === undefined) {
    throw new Error('useKYC must be used within a KYCProvider')
  }
  return context
}
