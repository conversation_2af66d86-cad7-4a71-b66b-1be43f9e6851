"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { 
  Newspaper, 
  Search, 
  Filter, 
  Clock, 
  TrendingUp, 
  TrendingDown, 
  Globe, 
  Calendar,
  Share2,
  Bookmark,
  ExternalLink,
  Loader2,
  RefreshCw,
  Eye,
  Star,
  AlertTriangle,
  DollarSign,
  BarChart3
} from "lucide-react"
import { useState, useEffect, useRef, memo } from "react"
import Link from "next/link"

// TradingView Widget Component
const TradingViewWidget = memo(() => {
  const container = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (container.current) {
      const script = document.createElement("script")
      script.src = "https://s3.tradingview.com/external-embedding/embed-widget-timeline.js"
      script.type = "text/javascript"
      script.async = true
      script.innerHTML = `
        {
          "displayMode": "adaptive",
          "feedMode": "all_symbols",
          "colorTheme": "dark",
          "isTransparent": true,
          "locale": "en",
          "width": 700,
          "height": 400
        }`
      container.current.appendChild(script)
    }
  }, [])

  return (
    <div className="tradingview-widget-container" ref={container}>
      <div className="tradingview-widget-container__widget"></div>
      <div className="tradingview-widget-copyright">
        <a href="https://www.tradingview.com/news-flow/?priority=top_stories" rel="noopener nofollow" target="_blank">
          <span className="blue-text">Top stories by TradingView</span>
        </a>
      </div>
    </div>
  )
})

TradingViewWidget.displayName = 'TradingViewWidget'

interface NewsArticle {
  id: string
  title: string
  summary: string
  content: string
  url: string
  source: string
  author?: string
  publishedAt: string
  category: string
  sentiment: "bullish" | "bearish" | "neutral"
  impact: "high" | "medium" | "low"
  symbols?: string[]
  imageUrl?: string
  readTime?: number
}

interface NewsCategory {
  id: string
  name: string
  icon: React.ReactNode
  count: number
}

export default function NewsPage() {
  const [news, setNews] = useState<NewsArticle[]>([])
  const [filteredNews, setFilteredNews] = useState<NewsArticle[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [selectedSentiment, setSelectedSentiment] = useState("all")
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)
  const [bookmarkedArticles, setBookmarkedArticles] = useState<string[]>([])

  // Sample news data (fallback if API fails)
  const sampleNews: NewsArticle[] = [
    {
      id: "1",
      title: "Federal Reserve Signals Potential Rate Cuts in 2024",
      summary: "The Federal Reserve has indicated a more dovish stance, suggesting potential interest rate cuts in the coming months, which could significantly impact forex markets.",
      content: "The Federal Reserve's latest monetary policy meeting revealed a shift toward a more accommodative stance, with officials signaling potential interest rate cuts in 2024. This dovish pivot comes amid cooling inflation and concerns about economic growth. The announcement has already begun to impact currency markets, with the US dollar showing weakness against major pairs.",
      url: "https://www.reuters.com/markets/us/federal-reserve-signals-potential-rate-cuts-2024",
      source: "Reuters",
      author: "Reuters Staff",
      publishedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
      category: "central-banks",
      sentiment: "bullish",
      impact: "high",
      symbols: ["USD", "EUR", "GBP", "JPY"],
      imageUrl: "https://images.unsplash.com/photo-*************-9c2a0a7236a3?w=800&h=400&fit=crop",
      readTime: 3
    },
    {
      id: "2",
      title: "ECB Maintains Current Interest Rates Amid Economic Uncertainty",
      summary: "The European Central Bank has decided to keep interest rates unchanged at their current levels, citing ongoing economic uncertainty in the Eurozone.",
      content: "The European Central Bank (ECB) has announced its decision to maintain current interest rates, citing ongoing economic uncertainty and the need for more data before making any policy changes. This decision comes as the Eurozone faces mixed economic indicators, with some countries showing signs of recovery while others continue to struggle.",
      url: "https://www.bloomberg.com/news/articles/ecb-maintains-current-interest-rates",
      source: "Bloomberg",
      author: "Bloomberg Staff",
      publishedAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago
      category: "central-banks",
      sentiment: "neutral",
      impact: "medium",
      symbols: ["EUR", "USD"],
      imageUrl: "https://images.unsplash.com/photo-**********-bebda4e38f71?w=800&h=400&fit=crop",
      readTime: 2
    },
    {
      id: "3",
      title: "Oil Prices Surge on Middle East Tensions",
      summary: "Crude oil prices have jumped significantly following escalating tensions in the Middle East, affecting commodity-linked currencies.",
      content: "Oil prices have surged by over 5% in today's trading session as tensions escalate in the Middle East. This significant move is affecting commodity-linked currencies, particularly the Canadian dollar and Norwegian krone, which are showing strength against the US dollar.",
      url: "https://www.cnbc.com/2024/oil-prices-surge-middle-east-tensions",
      source: "CNBC",
      author: "CNBC Staff",
      publishedAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(), // 1 hour ago
      category: "commodities",
      sentiment: "bearish",
      impact: "high",
      symbols: ["CAD", "NOK", "USD", "XAU"],
      imageUrl: "https://images.unsplash.com/photo-*************-48f60103fc96?w=800&h=400&fit=crop",
      readTime: 4
    },
    {
      id: "4",
      title: "UK Inflation Data Shows Surprise Drop",
      summary: "Latest UK inflation figures have come in below expectations, potentially influencing the Bank of England's monetary policy decisions.",
      content: "The UK's latest inflation data has surprised markets by showing a larger-than-expected drop in consumer prices. This development could influence the Bank of England's future monetary policy decisions and has already impacted GBP trading pairs.",
      url: "https://www.ft.com/content/uk-inflation-data-shows-surprise-drop",
      source: "Financial Times",
      author: "FT Staff",
      publishedAt: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(), // 3 hours ago
      category: "economic-data",
      sentiment: "bullish",
      impact: "medium",
      symbols: ["GBP", "EUR", "USD"],
      imageUrl: "https://images.unsplash.com/photo-**********-6726b3ff858f?w=800&h=400&fit=crop",
      readTime: 3
    },
    {
      id: "5",
      title: "Japanese Yen Weakens on BoJ Policy Uncertainty",
      summary: "The Japanese yen has weakened against major currencies as uncertainty grows over the Bank of Japan's future policy direction.",
      content: "The Japanese yen has continued its decline against major currencies as uncertainty grows over the Bank of Japan's future monetary policy direction. Analysts are closely watching for any signals from the central bank regarding potential policy changes.",
      url: "https://asia.nikkei.com/Markets/Currencies/Japanese-yen-weakens-on-BoJ-policy-uncertainty",
      source: "Nikkei",
      author: "Nikkei Staff",
      publishedAt: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(), // 5 hours ago
      category: "central-banks",
      sentiment: "bearish",
      impact: "medium",
      symbols: ["JPY", "USD", "EUR"],
      imageUrl: "https://images.unsplash.com/photo-**********-bebda4e38f71?w=800&h=400&fit=crop",
      readTime: 2
    },
    {
      id: "6",
      title: "Gold Reaches New All-Time High",
      summary: "Gold prices have surged to a new all-time high as investors seek safe-haven assets amid global economic uncertainty.",
      content: "Gold prices have reached a new all-time high as investors continue to seek safe-haven assets amid ongoing global economic uncertainty. The precious metal's strong performance is also being supported by central bank purchases and retail investor demand.",
      url: "https://www.marketwatch.com/story/gold-reaches-new-all-time-high",
      source: "MarketWatch",
      author: "MarketWatch Staff",
      publishedAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30 minutes ago
      category: "commodities",
      sentiment: "bullish",
      impact: "high",
      symbols: ["XAU", "USD", "EUR"],
      imageUrl: "https://images.unsplash.com/photo-*************-9c2a0a7236a3?w=800&h=400&fit=crop",
      readTime: 3
    }
  ]

  const categories: NewsCategory[] = [
    { id: "all", name: "All News", icon: <Newspaper className="w-4 h-4" />, count: news.length },
    { id: "central-banks", name: "Central Banks", icon: <DollarSign className="w-4 h-4" />, count: news.filter(n => n.category === "central-banks").length },
    { id: "economic-data", name: "Economic Data", icon: <BarChart3 className="w-4 h-4" />, count: news.filter(n => n.category === "economic-data").length },
    { id: "commodities", name: "Commodities", icon: <TrendingUp className="w-4 h-4" />, count: news.filter(n => n.category === "commodities").length },
    { id: "forex", name: "Forex", icon: <Globe className="w-4 h-4" />, count: news.filter(n => n.category === "forex").length }
  ]

  useEffect(() => {
    // Fetch real news from API
    const fetchNews = async () => {
      setLoading(true)
      setError(null)

      try {
        const params = new URLSearchParams({
          limit: '20',
          category: selectedCategory === 'all' ? 'all' : selectedCategory
        })

        const response = await fetch(`/api/news?${params}`)
        const data = await response.json()

        if (data.success && data.data) {
          setNews(data.data)
          setFilteredNews(data.data)
          setLastUpdated(new Date())
        } else {
          throw new Error(data.message || 'Failed to fetch news')
        }
      } catch (err) {
        setError("Failed to load news. Please try again later.")
        console.error("Error fetching news:", err)

        // Fallback to sample data if API fails
        setNews(sampleNews)
        setFilteredNews(sampleNews)
        setLastUpdated(new Date())
      } finally {
        setLoading(false)
      }
    }

    fetchNews()
  }, [selectedCategory])

  useEffect(() => {
    // Filter news based on search query and selected filters
    let filtered = news

    if (searchQuery) {
      filtered = filtered.filter(article =>
        article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        article.summary.toLowerCase().includes(searchQuery.toLowerCase()) ||
        article.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (article.symbols && article.symbols.some(symbol => symbol.toLowerCase().includes(searchQuery.toLowerCase())))
      )
    }

    if (selectedCategory !== "all") {
      filtered = filtered.filter(article => article.category === selectedCategory)
    }

    if (selectedSentiment !== "all") {
      filtered = filtered.filter(article => article.sentiment === selectedSentiment)
    }

    setFilteredNews(filtered)
  }, [news, searchQuery, selectedCategory, selectedSentiment])

  const handleRefresh = async () => {
    setLoading(true)
    setError(null)

    try {
      const params = new URLSearchParams({
        limit: '20',
        category: selectedCategory === 'all' ? 'all' : selectedCategory
      })

      const response = await fetch(`/api/news?${params}`)
      const data = await response.json()

      if (data.success && data.data) {
        setNews(data.data)
        setFilteredNews(data.data)
        setLastUpdated(new Date())
      } else {
        throw new Error(data.message || 'Failed to refresh news')
      }
    } catch (err) {
      setError("Failed to refresh news. Please try again later.")
      console.error("Error refreshing news:", err)
    } finally {
      setLoading(false)
    }
  }

  const toggleBookmark = (articleId: string) => {
    setBookmarkedArticles(prev => 
      prev.includes(articleId) 
        ? prev.filter(id => id !== articleId)
        : [...prev, articleId]
    )
  }

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case "bullish":
        return "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400"
      case "bearish":
        return "bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400"
      case "neutral":
        return "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300"
      default:
        return "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300"
    }
  }

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case "high":
        return "bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400"
      case "medium":
        return "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400"
      case "low":
        return "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400"
      default:
        return "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300"
    }
  }

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 1) return "Just now"
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`
    return `${Math.floor(diffInMinutes / 1440)}d ago`
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <div className="max-w-6xl mx-auto px-8 md:px-12 lg:px-16 py-24">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
              <p className="text-lg text-gray-600 dark:text-white/70">Loading latest trading news...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Header Section */}
      <section className="pt-24 pb-16 relative overflow-hidden">
        <div className="max-w-6xl mx-auto px-8 md:px-12 lg:px-16">
          {/* Background Effects */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-cyan-500/5 dark:from-blue-500/10 dark:to-cyan-500/10" />
          <div className="absolute top-20 left-10 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl animate-float" />
          <div className="absolute bottom-20 right-10 w-96 h-96 bg-cyan-500/10 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }} />
          
          <div className="relative z-10 text-center">
            <Badge
              variant="outline"
              className="mb-8 text-sm font-light border-blue-300 dark:border-blue-400/30 text-blue-600 dark:text-blue-400 px-4 py-2 items-center"
            >
              <Newspaper className="w-4 h-4 mr-2" />
              Real-Time News
            </Badge>
            
            <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold mb-8 leading-tight">
              Trading{" "}
              <span className="bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-400 dark:to-cyan-400 bg-clip-text text-transparent">
                News
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-gray-700 dark:text-white/70 mb-12 max-w-4xl mx-auto leading-relaxed">
              Stay ahead of the markets with real-time trading news, market analysis, and economic updates from trusted sources worldwide.
            </p>

            {/* Search and Filters */}
            <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-6 shadow-xl max-w-4xl mx-auto">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-10 h-10 rounded-xl bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center">
                  <Search className="w-5 h-5 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 dark:text-white">Find News</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Search */}
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <Input
                    type="text"
                    placeholder="Search news..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="rounded-xl border-gray-200 dark:border-white/20 bg-white/50 dark:bg-white/5 pl-10"
                  />
                </div>

                {/* Category Filter */}
                <div>
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="w-full rounded-xl border-gray-200 dark:border-white/20 bg-white/50 dark:bg-white/5 text-gray-700 dark:text-white/70 px-4 py-2"
                  >
                    {categories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.name} ({category.count})
                      </option>
                    ))}
                  </select>
                </div>

                {/* Sentiment Filter */}
                <div>
                  <select
                    value={selectedSentiment}
                    onChange={(e) => setSelectedSentiment(e.target.value)}
                    className="w-full rounded-xl border-gray-200 dark:border-white/20 bg-white/50 dark:bg-white/5 text-gray-700 dark:text-white/70 px-4 py-2"
                  >
                    <option value="all">All Sentiments</option>
                    <option value="bullish">Bullish</option>
                    <option value="bearish">Bearish</option>
                    <option value="neutral">Neutral</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* TradingView News Widget */}
      <section className="py-16 relative">
        <div className="max-w-6xl mx-auto px-8 md:px-12 lg:px-16">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden border border-gray-200 dark:border-gray-700">
            {/* Widget Header */}
            <div className="bg-gray-50 dark:bg-gray-900 px-6 py-6 border-b border-gray-200 dark:border-gray-700">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-3 mb-3">
                <Globe className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                TradingView Market News
              </h2>
              <p className="text-gray-600 dark:text-gray-300 text-lg leading-relaxed">
                Real-time market news and analysis from TradingView's comprehensive news feed. Stay updated with the latest market-moving stories and expert insights.
              </p>
            </div>

            {/* TradingView Widget */}
            <div className="p-8 flex justify-center bg-white dark:bg-gray-800">
              <TradingViewWidget />
            </div>

            {/* Widget Features */}
            <div className="bg-gray-50 dark:bg-gray-900 px-8 py-6 border-t border-gray-200 dark:border-gray-700">
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">News Features</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Globe className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-1">Global Coverage</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-300">Comprehensive news from all major markets</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Clock className="w-4 h-4 text-green-600 dark:text-green-400" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-1">Real-time Updates</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-300">Live news feed with instant updates</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center flex-shrink-0">
                    <BarChart3 className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-1">Market Analysis</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-300">Expert insights and market commentary</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* News Content */}
      <section className="py-16 relative">
        <div className="max-w-6xl mx-auto px-8 md:px-12 lg:px-16">
          {/* Header with refresh */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 rounded-2xl bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center shadow-lg">
                <Newspaper className="w-6 h-6 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Latest News</h2>
                <p className="text-sm text-gray-600 dark:text-white/60">
                  {filteredNews.length} articles • Real-time updates
                  {lastUpdated && (
                    <span className="ml-2">• Last updated: {lastUpdated.toLocaleTimeString()}</span>
                  )}
                </p>
              </div>
            </div>
            <Button 
              onClick={handleRefresh}
              disabled={loading}
              className="bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-500 dark:to-cyan-500 text-white hover:from-blue-700 hover:to-cyan-700 dark:hover:from-blue-600 dark:hover:to-cyan-600"
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>

          {error && (
            <div className="mb-8 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <div className="flex items-center gap-2">
                <AlertTriangle className="w-5 h-5 text-red-600 dark:text-red-400" />
                <p className="text-red-700 dark:text-red-400">{error}</p>
              </div>
            </div>
          )}

          {/* News Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {filteredNews.length > 0 ? (
              filteredNews.map((article) => (
                <Card key={article.id} className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
                  <CardHeader>
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center gap-2">
                        <Badge className={getSentimentColor(article.sentiment)}>
                          {article.sentiment.charAt(0).toUpperCase() + article.sentiment.slice(1)}
                        </Badge>
                        <Badge className={getImpactColor(article.impact)}>
                          {article.impact.charAt(0).toUpperCase() + article.impact.slice(1)} Impact
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleBookmark(article.id)}
                          className="text-gray-500 hover:text-yellow-500"
                        >
                          <Bookmark className={`w-4 h-4 ${bookmarkedArticles.includes(article.id) ? 'fill-current text-yellow-500' : ''}`} />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-gray-500 hover:text-blue-500"
                        >
                          <Share2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                    
                    <CardTitle className="text-xl font-bold text-gray-900 dark:text-white mb-3 line-clamp-2">
                      {article.title}
                    </CardTitle>
                    
                    <CardDescription className="text-gray-700 dark:text-white/70 mb-4 line-clamp-3">
                      {article.summary}
                    </CardDescription>

                    <div className="flex items-center justify-between text-sm text-gray-600 dark:text-white/60">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-1">
                          <Clock className="w-4 h-4" />
                          {formatTimeAgo(article.publishedAt)}
                        </div>
                        <div className="flex items-center gap-1">
                          <Eye className="w-4 h-4" />
                          {article.readTime} min read
                        </div>
                      </div>
                      <div className="font-medium">{article.source}</div>
                    </div>
                  </CardHeader>
                  
                  <CardContent>
                    <div className="mb-4">
                      <h4 className="text-sm font-semibold text-gray-700 dark:text-white/70 mb-2">Related Symbols:</h4>
                      <div className="flex flex-wrap gap-2">
                        {article.symbols?.map((symbol, index) => (
                          <Badge key={index} variant="outline" className="border-blue-300 dark:border-blue-400/30 text-blue-600 dark:text-blue-400">
                            {symbol}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    
                    <div className="flex gap-2">
                      <Button
                        onClick={() => window.open(article.url, '_blank')}
                        className="flex-1 bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-500 dark:to-cyan-500 text-white hover:from-blue-700 hover:to-cyan-700 dark:hover:from-blue-600 dark:hover:to-cyan-600"
                      >
                        <ExternalLink className="w-4 h-4 mr-2" />
                        Read Full Article
                      </Button>
                      <Button
                        variant="outline"
                        className="border-gray-300 dark:border-white/20 text-gray-700 dark:text-white/70 hover:bg-gray-50 dark:hover:bg-white/5"
                      >
                        <Star className="w-4 h-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <div className="col-span-full text-center py-12">
                <Newspaper className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">No news found</h3>
                <p className="text-gray-600 dark:text-white/60">
                  Try adjusting your search criteria or filters
                </p>
              </div>
            )}
          </div>

          {/* Market Sentiment Overview */}
          <div className="mt-16">
            <div className="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20 backdrop-blur-sm border border-blue-200 dark:border-blue-400/20 rounded-3xl p-8 shadow-xl">
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Market Sentiment Overview</h3>
                <p className="text-gray-700 dark:text-white/70">
                  Current market sentiment based on latest news analysis
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="w-16 h-16 rounded-2xl bg-green-100 dark:bg-green-900/30 flex items-center justify-center mx-auto mb-4">
                    <TrendingUp className="w-8 h-8 text-green-600 dark:text-green-400" />
                  </div>
                  <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">
                    {news.filter(n => n.sentiment === "bullish").length}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-white/60">Bullish News</div>
                </div>
                
                <div className="text-center">
                  <div className="w-16 h-16 rounded-2xl bg-red-100 dark:bg-red-900/30 flex items-center justify-center mx-auto mb-4">
                    <TrendingDown className="w-8 h-8 text-red-600 dark:text-red-400" />
                  </div>
                  <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">
                    {news.filter(n => n.sentiment === "bearish").length}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-white/60">Bearish News</div>
                </div>
                
                <div className="text-center">
                  <div className="w-16 h-16 rounded-2xl bg-gray-100 dark:bg-gray-800 flex items-center justify-center mx-auto mb-4">
                    <BarChart3 className="w-8 h-8 text-gray-600 dark:text-gray-400" />
                  </div>
                  <div className="text-3xl font-bold text-gray-600 dark:text-gray-400 mb-2">
                    {news.filter(n => n.sentiment === "neutral").length}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-white/60">Neutral News</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
} 