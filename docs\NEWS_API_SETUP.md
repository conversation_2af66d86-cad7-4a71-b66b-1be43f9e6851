# Trading News API Setup Guide

This guide explains how to set up real-time trading news from multiple sources including TradingView, Alpha Vantage, and other financial data providers.

## Overview

The news system fetches real trading news from multiple sources and displays them in:
- `/news` - Full news page with filtering and search
- Dashboard Resources section - Latest 3 news items

## Supported News Sources

### 1. Alpha Vantage (Recommended)
- **Free Tier**: 25 requests/day
- **Features**: News sentiment analysis, financial market news
- **Sign up**: https://www.alphavantage.co/support/#api-key
- **Environment Variable**: `ALPHA_VANTAGE_API_KEY`

### 2. Polygon.io
- **Free Tier**: 5 calls/minute
- **Features**: Market news, ticker-specific news
- **Sign up**: https://polygon.io/
- **Environment Variable**: `POLYGON_API_KEY`

### 3. NewsAPI
- **Free Tier**: 1000 requests/month
- **Features**: General financial news from major outlets (Reuters, Bloomberg, CNBC)
- **Sign up**: https://newsapi.org/register
- **Environment Variable**: `NEWS_API_KEY`

### 4. Financial Modeling Prep
- **Free Tier**: 250 requests/day
- **Features**: Financial articles and market analysis
- **Sign up**: https://financialmodelingprep.com/developer/docs
- **Environment Variable**: `FMP_API_KEY`

### 5. Finnhub
- **Free Tier**: 60 calls/minute
- **Features**: Market news and company news
- **Sign up**: https://finnhub.io/register
- **Environment Variable**: `FINNHUB_API_KEY`

### 6. TradingView (RSS Fallback)
- **Free**: RSS feed access
- **Features**: Trading ideas and market analysis
- **Note**: Uses public RSS feed, no API key required
- **Premium API**: Contact TradingView for API access

## Setup Instructions

### 1. Copy Environment File
```bash
cp .env.example .env.local
```

### 2. Add API Keys
Edit `.env.local` and add your API keys:

```env
# Minimum recommended setup (free tiers)
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key
NEWS_API_KEY=your_news_api_key
POLYGON_API_KEY=your_polygon_key

# Optional additional sources
FMP_API_KEY=your_fmp_key
FINNHUB_API_KEY=your_finnhub_key
```

### 3. Restart Development Server
```bash
npm run dev
```

## API Endpoints

### GET /api/news
Fetches trading news from configured sources.

**Parameters:**
- `limit` (optional): Number of articles to return (default: 20)
- `category` (optional): Filter by category (default: 'all')
- `source` (optional): Filter by source (default: 'all')

**Example:**
```
GET /api/news?limit=10&category=central-banks
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "unique-id",
      "title": "Article Title",
      "summary": "Article summary...",
      "content": "Full article content...",
      "url": "https://source.com/article",
      "source": "Reuters",
      "author": "Author Name",
      "publishedAt": "2024-01-01T12:00:00Z",
      "category": "central-banks",
      "sentiment": "bullish|bearish|neutral",
      "impact": "high|medium|low",
      "symbols": ["USD", "EUR"],
      "imageUrl": "https://image.url",
      "readTime": 3
    }
  ],
  "total": 10,
  "timestamp": "2024-01-01T12:00:00Z",
  "sources": ["Reuters", "Bloomberg"],
  "categories": ["central-banks", "forex"]
}
```

## Fallback Behavior

If no API keys are configured or all APIs fail:
1. System shows placeholder articles with setup instructions
2. Users can still navigate the news interface
3. No errors are thrown to the user

## Rate Limiting

The system respects API rate limits:
- Tries sources in priority order
- Stops when enough articles are fetched
- Caches results to minimize API calls
- Falls back gracefully if limits are exceeded

## Categories

News articles are categorized as:
- `central-banks` - Federal Reserve, ECB, BoJ decisions
- `economic-data` - GDP, inflation, employment data
- `commodities` - Oil, gold, agricultural products
- `forex` - Currency pair analysis
- `technical-analysis` - Chart patterns, indicators
- `financial_markets` - General market news
- `system` - Configuration messages

## Troubleshooting

### No News Showing
1. Check `.env.local` file exists and has API keys
2. Verify API keys are valid
3. Check browser console for errors
4. Ensure development server was restarted after adding keys

### API Errors
- Check API key validity
- Verify you haven't exceeded rate limits
- Check network connectivity
- Review server logs for detailed error messages

### Performance Issues
- Reduce the `limit` parameter in API calls
- Consider caching strategies for production
- Monitor API usage to stay within free tier limits

## Production Considerations

1. **Caching**: Implement Redis or similar for API response caching
2. **Rate Limiting**: Add request throttling to prevent API limit exhaustion
3. **Error Handling**: Implement retry logic with exponential backoff
4. **Monitoring**: Track API usage and response times
5. **Backup Sources**: Ensure multiple API keys are configured for redundancy
